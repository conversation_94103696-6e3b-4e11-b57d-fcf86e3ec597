import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

const routes: Array<RouteRecordRaw> = [
  // 登录页面
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '用户登录',
      description: '管理员登录页面',
      requiresAuth: false,
      layout: false
    }
  },

  // 主布局路由
  {
    path: '/',
    name: 'TabsLayout',
    component: () => import('@/components/TabsLayout.vue'),
    redirect: '/dashboard',
    meta: {
      requiresAuth: true,
      layout: true
    },
    children: [
      // 仪表板
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: {
          title: '仪表板',
          description: '系统概览和统计信息',
          icon: 'Odometer',
          requiresAuth: true,
          order: 1
        }
      },

      // 用户管理
      {
        path: 'users',
        name: 'Users',
        component: () => import('@/views/Users.vue'),
        meta: {
          title: '用户管理',
          description: '管理系统用户信息',
          icon: 'User',
          requiresAuth: true,
          order: 2
        }
      },
      {
        path: 'users/:id',
        name: 'UserDetail',
        component: () => import('@/views/UserDetail.vue'),
        meta: {
          title: '用户详情',
          description: '查看和编辑用户详细信息',
          icon: 'User',
          requiresAuth: true,
          hidden: true,
          breadcrumb: [
            { title: '用户管理', path: '/users' },
            { title: '用户详情' }
          ]
        }
      },



      // 个人资料
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/Profile.vue'),
        meta: {
          title: '管理员资料',
          description: '管理管理员信息和账户设置',
          icon: 'User',
          requiresAuth: false,
          hidden: true, // 隐藏在侧边栏中，只通过顶部用户菜单访问
          order: 4
        }
      },

      // 系统监控
      {
        path: 'system-monitoring',
        name: 'SystemMonitoring',
        component: () => import('@/views/system-monitoring/SystemMonitoringLayout.vue'),
        redirect: '/system-monitoring/overview',
        meta: {
          title: '系统监控',
          description: '实时监控系统状态，确保服务稳定运行',
          icon: 'Monitor',
          requiresAuth: true,
          roles: ['admin', 'superadmin'],
          order: 10
        },
        children: [
          {
            path: 'overview',
            name: 'SystemMonitoringOverview',
            component: () => import('@/views/system-monitoring/index.vue'),
            meta: {
              title: '监控概览',
              description: '系统监控功能概览和关键指标',
              icon: 'DataBoard',
              requiresAuth: true,
              order: 0
            }
          },
          {
            path: 'logs',
            name: 'LogManagement',
            component: () => import('@/views/system-monitoring/LogManagement.vue'),
            meta: {
              title: '日志管理',
              description: '系统日志查看、搜索和管理',
              icon: 'Document',
              requiresAuth: true,
              order: 1
            }
          },
          {
            path: 'performance',
            name: 'PerformanceMonitoring',
            component: () => import('@/views/system-monitoring/PerformanceMonitoring.vue'),
            meta: {
              title: '性能监控',
              description: '系统性能指标监控和分析',
              icon: 'TrendCharts',
              requiresAuth: true,
              order: 1
            }
          }
        ]
      },

      // 数据管理
      {
        path: 'data-management',
        name: 'DataManagement',
        component: () => import('@/views/data-management/DataManagementLayout.vue'),
        redirect: '/data-management/backup',
        meta: {
          title: '数据管理',
          description: '数据备份、分析和同步管理',
          icon: 'FolderOpened',
          requiresAuth: true,
          roles: ['admin', 'superadmin'],
          order: 11
        },
        children: [
          {
            path: 'backup',
            name: 'DataBackup',
            component: () => import('@/views/data-management/DataBackup.vue'),
            meta: {
              title: '数据备份与清理',
              description: '管理数据备份、清理和恢复操作',
              icon: 'FolderAdd',
              requiresAuth: true,
              roles: ['admin', 'superadmin'],
              order: 2
            }
          },
          {
            path: 'analysis',
            name: 'DataAnalysis',
            component: () => import('@/views/data-management/DataAnalysis.vue'),
            meta: {
              title: '数据分析',
              description: '查看数据统计和分析报告',
              icon: 'TrendCharts',
              requiresAuth: true,
              roles: ['admin', 'superadmin'],
              order: 2
            }
          },
          {
            path: 'sync',
            name: 'DataSync',
            component: () => import('@/views/DataSync.vue'),
            meta: {
              title: '数据同步',
              description: '管理Android应用与后台数据库的数据同步',
              icon: 'Refresh',
              requiresAuth: true,
              roles: ['admin', 'superadmin'],
              order: 4
            }
          }
        ]
      },



      // 社区管理
      {
        path: 'community',
        name: 'Community',
        component: () => import('@/views/community/CommunityLayout.vue'),
        redirect: '/community/overview',
        meta: {
          title: '社区管理',
          description: '管理学习社区的各项功能',
          icon: 'ChatDotRound',
          requiresAuth: true,
          order: 8
        },
        children: [
          // 社区概览
          {
            path: 'overview',
            name: 'CommunityOverview',
            component: () => import('@/views/community/index.vue'),
            meta: {
              title: '社区概览',
              description: '社区管理功能概览和统计信息',
              icon: 'DataBoard',
              requiresAuth: true,
              order: 0
            }
          },
          // 资源库管理
          {
            path: 'resources',
            name: 'CommunityResources',
            component: () => import('@/views/community/Resources.vue'),
            meta: {
              title: '资源库管理',
              description: '管理学习资源、文档、视频等内容',
              icon: 'FolderOpened',
              requiresAuth: true,
              order: 1
            }
          },
          // 自习室管理
          {
            path: 'study-rooms',
            name: 'CommunityStudyRooms',
            component: () => import('@/views/community/StudyRooms.vue'),
            meta: {
              title: '自习室管理',
              description: '管理虚拟自习室创建、监控和统计',
              icon: 'School',
              requiresAuth: true,
              order: 2
            }
          },
          // 话题管理
          {
            path: 'topics',
            name: 'CommunityTopics',
            component: () => import('@/views/community/Topics.vue'),
            meta: {
              title: '话题管理',
              description: '管理社区话题的创建、审核和分类',
              icon: 'ChatLineRound',
              requiresAuth: true,
              order: 3
            }
          },
          // 部落管理
          {
            path: 'tribes',
            name: 'CommunityTribes',
            component: () => import('@/views/community/Tribes.vue'),
            meta: {
              title: '部落管理',
              description: '管理学习部落/群组功能',
              icon: 'UserFilled',
              requiresAuth: true,
              order: 4
            }
          },
          // 师徒结对
          {
            path: 'mentorship',
            name: 'CommunityMentorship',
            component: () => import('@/views/community/Mentorship.vue'),
            meta: {
              title: '师徒结对',
              description: '管理师徒关系匹配和进度跟踪',
              icon: 'Avatar',
              requiresAuth: true,
              order: 5
            }
          },
          // 学霸经验
          {
            path: 'experiences',
            name: 'CommunityExperiences',
            component: () => import('@/views/community/Experiences.vue'),
            meta: {
              title: '学霸经验',
              description: '管理优秀学习经验分享',
              icon: 'TrophyBase',
              requiresAuth: true,
              order: 6
            }
          },
          // 活动竞赛
          {
            path: 'activities',
            name: 'CommunityActivities',
            component: () => import('@/views/community/Activities.vue'),
            meta: {
              title: '活动竞赛',
              description: '管理学习活动和竞赛组织',
              icon: 'Medal',
              requiresAuth: true,
              order: 7
            }
          },
          // 心愿墙
          {
            path: 'wishes',
            name: 'CommunityWishes',
            component: () => import('@/views/community/Wishes.vue'),
            meta: {
              title: '心愿墙',
              description: '管理用户心愿发布和实现',
              icon: 'MagicStick',
              requiresAuth: true,
              order: 8
            }
          },
          // 游戏圈
          {
            path: 'games',
            name: 'CommunityGames',
            component: () => import('@/views/community/Games.vue'),
            meta: {
              title: '游戏圈',
              description: '管理学习游戏化功能',
              icon: 'Present',
              requiresAuth: true,
              order: 9
            }
          }
        ]
      },

      // 系统设置
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/Settings.vue'),
        meta: {
          title: '系统设置',
          description: '配置系统参数和选项',
          icon: 'Setting',
          requiresAuth: true,
          requiresSuperuser: true,
          order: 12
        }
      },



      // 广告管理
      {
        path: 'advertising',
        name: 'Advertising',
        component: () => import('@/views/advertising/AdvertisingLayout.vue'),
        redirect: '/advertising/overview',
        meta: {
          title: '广告管理',
          description: '管理轮播图、弹窗等广告内容',
          icon: 'Picture',
          requiresAuth: true,
          order: 3
        },
        children: [
          // 广告概览
          {
            path: 'overview',
            name: 'AdvertisingOverview',
            component: () => import('@/views/advertising/index.vue'),
            meta: {
              title: '广告概览',
              description: '广告管理功能概览和统计信息',
              icon: 'DataBoard',
              requiresAuth: true,
              order: 0
            }
          },
          // 轮播图管理
          {
            path: 'banners',
            name: 'AdvertisingBanners',
            component: () => import('@/views/advertising/Banners.vue'),
            meta: {
              title: '轮播图管理',
              description: '管理首页轮播图和广告横幅',
              icon: 'Picture',
              requiresAuth: true,
              order: 1
            }
          },
          // 弹窗管理
          {
            path: 'popups',
            name: 'AdvertisingPopups',
            component: () => import('@/views/advertising/Popups.vue'),
            meta: {
              title: '弹窗管理',
              description: '管理应用内弹窗广告',
              icon: 'Monitor',
              requiresAuth: true,
              order: 2
            }
          }
        ]
      },

      // 题库管理
      {
        path: 'question-bank',
        name: 'QuestionBank',
        component: () => import('@/views/question-bank/QuestionBankLayout.vue'),
        redirect: '/question-bank/overview',
        meta: {
          title: '题库管理',
          description: '管理题目分类、内容和组卷功能',
          icon: 'EditPen',
          requiresAuth: true,
          order: 4
        },
        children: [
          // 题库概览
          {
            path: 'overview',
            name: 'QuestionBankOverview',
            component: () => import('@/views/question-bank/index.vue'),
            meta: {
              title: '题库概览',
              description: '题库管理功能概览和统计信息',
              icon: 'DataBoard',
              requiresAuth: true,
              order: 0
            }
          },
          // 题目分类
          {
            path: 'categories',
            name: 'QuestionCategories',
            component: () => import('@/views/question-bank/Categories.vue'),
            meta: {
              title: '题目分类',
              description: '管理题目的学科和难度分类',
              icon: 'Menu',
              requiresAuth: true,
              order: 1
            }
          },
          // 题目管理
          {
            path: 'questions',
            name: 'Questions',
            component: () => import('@/views/question-bank/Questions.vue'),
            meta: {
              title: '题目管理',
              description: '管理题目内容和题型',
              icon: 'EditPen',
              requiresAuth: true,
              order: 2
            }
          },
          // 试卷管理
          {
            path: 'papers',
            name: 'QuestionPapers',
            component: () => import('@/views/question-bank/Papers.vue'),
            meta: {
              title: '试卷管理',
              description: '管理试卷组卷和发布',
              icon: 'Document',
              requiresAuth: true,
              order: 3
            }
          },
          // 答题统计
          {
            path: 'statistics',
            name: 'QuestionStatistics',
            component: () => import('@/views/question-bank/Statistics.vue'),
            meta: {
              title: '答题统计',
              description: '查看答题数据和分析报告',
              icon: 'TrendCharts',
              requiresAuth: true,
              order: 4
            }
          }
        ]
      },

      // 英语练习管理
      {
        path: 'english-practice',
        name: 'EnglishPractice',
        component: () => import('@/views/english-practice/EnglishPracticeLayout.vue'),
        redirect: '/english-practice/overview',
        meta: {
          title: '英语练习管理',
          description: '管理词汇、听力、口语等英语练习内容',
          icon: 'ChatDotSquare',
          requiresAuth: true,
          order: 5
        },
        children: [
          // 英语练习概览
          {
            path: 'overview',
            name: 'EnglishPracticeOverview',
            component: () => import('@/views/english-practice/index.vue'),
            meta: {
              title: '英语练习概览',
              description: '英语练习管理功能概览和统计信息',
              icon: 'DataBoard',
              requiresAuth: true,
              order: 0
            }
          },
          // 词汇库管理
          {
            path: 'vocabulary',
            name: 'EnglishVocabulary',
            component: () => import('@/views/english-practice/Vocabulary.vue'),
            meta: {
              title: '词汇库管理',
              description: '管理英语单词和词汇分级',
              icon: 'Notebook',
              requiresAuth: true,
              order: 1
            }
          },
          // 听力材料
          {
            path: 'listening',
            name: 'EnglishListening',
            component: () => import('@/views/english-practice/Listening.vue'),
            meta: {
              title: '听力材料',
              description: '管理听力音频和文本对照',
              icon: 'Headset',
              requiresAuth: true,
              order: 2
            }
          },
          // 口语练习
          {
            path: 'speaking',
            name: 'EnglishSpeaking',
            component: () => import('@/views/english-practice/Speaking.vue'),
            meta: {
              title: '口语练习',
              description: '管理口语练习场景和评测标准',
              icon: 'Microphone',
              requiresAuth: true,
              order: 3
            }
          },
          // 学习进度
          {
            path: 'progress',
            name: 'EnglishProgress',
            component: () => import('@/views/english-practice/Progress.vue'),
            meta: {
              title: '学习进度',
              description: '跟踪用户英语学习进度和成绩',
              icon: 'TrendCharts',
              requiresAuth: true,
              order: 4
            }
          }
        ]
      },

      // 心理资源库管理
      {
        path: 'psychology',
        name: 'Psychology',
        component: () => import('@/views/psychology/PsychologyLayout.vue'),
        redirect: '/psychology/overview',
        meta: {
          title: '心理资源库',
          description: '管理心理测评、文章和咨询预约',
          icon: 'MagicStick',
          requiresAuth: true,
          order: 6
        },
        children: [
          // 心理资源概览
          {
            path: 'overview',
            name: 'PsychologyOverview',
            component: () => import('@/views/psychology/index.vue'),
            meta: {
              title: '心理资源概览',
              description: '心理资源库管理功能概览和统计信息',
              icon: 'DataBoard',
              requiresAuth: true,
              order: 0
            }
          },
          // 测评量表管理
          {
            path: 'assessments',
            name: 'PsychologyAssessments',
            component: () => import('@/views/psychology/Assessments.vue'),
            meta: {
              title: '测评量表管理',
              description: '管理心理测评量表和评分规则',
              icon: 'List',
              requiresAuth: true,
              order: 1
            }
          },
          // 心理文章管理
          {
            path: 'articles',
            name: 'PsychologyArticles',
            component: () => import('@/views/psychology/Articles.vue'),
            meta: {
              title: '心理文章管理',
              description: '管理心理健康文章和推荐',
              icon: 'Document',
              requiresAuth: true,
              order: 2
            }
          },
          // 咨询预约管理
          {
            path: 'appointments',
            name: 'PsychologyAppointments',
            component: () => import('@/views/psychology/Appointments.vue'),
            meta: {
              title: '咨询预约管理',
              description: '管理心理咨询预约和咨询师',
              icon: 'Calendar',
              requiresAuth: true,
              order: 3
            }
          },
          // 危机预警系统
          {
            path: 'crisis',
            name: 'PsychologyCrisis',
            component: () => import('@/views/psychology/Crisis.vue'),
            meta: {
              title: '危机预警系统',
              description: '管理心理危机预警和干预',
              icon: 'AlarmClock',
              requiresAuth: true,
              order: 4
            }
          }
        ]
      },

      // 课程管理
      {
        path: 'courses',
        name: 'Courses',
        component: () => import('@/views/courses/CoursesLayout.vue'),
        redirect: '/courses/overview',
        meta: {
          title: '课程管理',
          description: '管理课程内容、讲师和学习进度',
          icon: 'Reading',
          requiresAuth: true,
          order: 7
        },
        children: [
          // 课程概览
          {
            path: 'overview',
            name: 'CoursesOverview',
            component: () => import('@/views/courses/index.vue'),
            meta: {
              title: '课程概览',
              description: '课程管理功能概览和统计信息',
              icon: 'DataBoard',
              requiresAuth: true,
              order: 0
            }
          },
          // 课程内容管理
          {
            path: 'content',
            name: 'CoursesContent',
            component: () => import('@/views/courses/Content.vue'),
            meta: {
              title: '课程内容管理',
              description: '管理课程视频和章节内容',
              icon: 'VideoPlay',
              requiresAuth: true,
              order: 1
            }
          },
          // 讲师管理
          {
            path: 'instructors',
            name: 'CoursesInstructors',
            component: () => import('@/views/courses/Instructors.vue'),
            meta: {
              title: '讲师管理',
              description: '管理讲师信息和资质认证',
              icon: 'User',
              requiresAuth: true,
              order: 2
            }
          },
          // 学习进度管理
          {
            path: 'progress',
            name: 'CoursesProgress',
            component: () => import('@/views/courses/Progress.vue'),
            meta: {
              title: '学习进度管理',
              description: '跟踪用户学习进度和完成度',
              icon: 'TrendCharts',
              requiresAuth: true,
              order: 3
            }
          },
          // 课程评价
          {
            path: 'evaluations',
            name: 'CoursesEvaluations',
            component: () => import('@/views/courses/Evaluations.vue'),
            meta: {
              title: '课程评价',
              description: '管理课程评分和用户反馈',
              icon: 'Star',
              requiresAuth: true,
              order: 4
            }
          }
        ]
      },

      // 软件更新推送
      {
        path: 'software-update',
        name: 'SoftwareUpdate',
        component: () => import('@/views/software-update/SoftwareUpdateLayout.vue'),
        redirect: '/software-update/overview',
        meta: {
          title: '软件更新推送',
          description: '管理应用版本更新和推送策略',
          icon: 'Refresh',
          requiresAuth: true,
          order: 9
        },
        children: [
          // 更新概览
          {
            path: 'overview',
            name: 'SoftwareUpdateOverview',
            component: () => import('@/views/software-update/index.vue'),
            meta: {
              title: '更新概览',
              description: '软件更新推送功能概览和统计信息',
              icon: 'DataBoard',
              requiresAuth: true,
              order: 0
            }
          },
          // 版本管理
          {
            path: 'versions',
            name: 'SoftwareVersions',
            component: () => import('@/views/software-update/Versions.vue'),
            meta: {
              title: '版本管理',
              description: '管理应用版本和更新内容',
              icon: 'SetUp',
              requiresAuth: true,
              order: 1
            }
          },
          // 推送策略
          {
            path: 'strategies',
            name: 'SoftwareUpdateStrategies',
            component: () => import('@/views/software-update/Strategies.vue'),
            meta: {
              title: '推送策略',
              description: '管理更新推送策略和灰度发布',
              icon: 'Connection',
              requiresAuth: true,
              order: 2
            }
          },
          // 更新统计
          {
            path: 'statistics',
            name: 'SoftwareUpdateStatistics',
            component: () => import('@/views/software-update/Statistics.vue'),
            meta: {
              title: '更新统计',
              description: '查看更新下载和安装数据',
              icon: 'TrendCharts',
              requiresAuth: true,
              order: 3
            }
          }
        ]
      }
    ]
  },

  // 错误页面
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/404.vue'),
    meta: {
      title: '页面不存在',
      description: '您访问的页面不存在',
      requiresAuth: false,
      layout: false
    }
  },

  // 通配符路由 - 必须放在最后
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()

  const authStore = useAuthStore()

  // 调试信息
  console.log(`[Router] 导航: ${from.path} -> ${to.path}`, {
    requiresAuth: to.meta.requiresAuth,
    requiresSuperuser: to.meta.requiresSuperuser,
    isAuthenticated: authStore.isAuthenticated
  })

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - WisCude 管理系统`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    // 如果已经认证，直接检查权限
    if (authStore.isAuthenticated) {
      // 检查超级用户权限
      if (to.meta.requiresSuperuser && !authStore.user?.is_superuser) {
        next('/404')
        return
      }
      next()
      return
    }

    // 未认证，尝试从本地存储恢复登录状态
    const token = localStorage.getItem('access_token')
    if (token) {
      try {
        const isAuthenticated = await authStore.checkAuth()
        if (isAuthenticated) {
          // 认证成功后检查权限
          if (to.meta.requiresSuperuser && !authStore.user?.is_superuser) {
            next('/404')
            return
          }
          next()
          return
        }
      } catch (error) {
        console.error('认证检查失败:', error)
        // 清除无效的令牌
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
      }
    }

    // 未登录，跳转到登录页
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
    return
  }

  // 已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && authStore.isAuthenticated) {
    next('/')
    return
  }

  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
