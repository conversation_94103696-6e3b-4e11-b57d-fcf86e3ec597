<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志模块API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #ecf0f1;
        }
        .loading {
            color: #3498db;
        }
        .success {
            color: #27ae60;
            background: #d5f4e6;
        }
        .error {
            color: #e74c3c;
            background: #fdf2f2;
        }
        .warning {
            color: #f39c12;
            background: #fef9e7;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        .modules-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        .module-tag {
            background: #3498db;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .default-tag {
            background: #f39c12;
        }
        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>日志模块API测试</h1>
        
        <div class="card">
            <h2>API测试控制</h2>
            <button onclick="testLogModulesAPI()" id="testBtn">测试日志模块API</button>
            <button onclick="testWithAuth()" id="authBtn">使用认证测试</button>
            <button onclick="clearResults()" id="clearBtn">清除结果</button>
            <div class="status" id="status">准备就绪</div>
        </div>

        <div class="card">
            <h2>模块列表结果</h2>
            <div id="modulesList" class="modules-list">
                <span style="color: #7f8c8d;">等待测试结果...</span>
            </div>
        </div>

        <div class="card">
            <h2>API响应详情</h2>
            <pre id="responseDetails">等待API调用...</pre>
        </div>

        <div class="card">
            <h2>错误处理测试</h2>
            <p>这个测试验证了以下错误处理场景：</p>
            <ul>
                <li><strong>401 Unauthorized</strong>: 未认证访问</li>
                <li><strong>404 Not Found</strong>: 接口不存在</li>
                <li><strong>500 Server Error</strong>: 服务器内部错误</li>
                <li><strong>Network Error</strong>: 网络连接问题</li>
            </ul>
        </div>
    </div>

    <script>
        let isLoading = false;

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function updateModulesList(modules, isDefault = false) {
            const container = document.getElementById('modulesList');
            container.innerHTML = '';
            
            if (modules && modules.length > 0) {
                modules.forEach(module => {
                    const tag = document.createElement('span');
                    tag.className = `module-tag ${isDefault ? 'default-tag' : ''}`;
                    tag.textContent = module;
                    container.appendChild(tag);
                });
                
                if (isDefault) {
                    const note = document.createElement('div');
                    note.style.marginTop = '10px';
                    note.style.fontSize = '12px';
                    note.style.color = '#f39c12';
                    note.textContent = '⚠️ 使用默认模块列表（API调用失败）';
                    container.appendChild(note);
                }
            } else {
                container.innerHTML = '<span style="color: #e74c3c;">无模块数据</span>';
            }
        }

        function updateResponseDetails(data) {
            const pre = document.getElementById('responseDetails');
            pre.textContent = JSON.stringify(data, null, 2);
        }

        async function testLogModulesAPI() {
            if (isLoading) return;
            
            isLoading = true;
            document.getElementById('testBtn').disabled = true;
            updateStatus('正在测试日志模块API...', 'loading');
            
            try {
                const response = await fetch('/api/system/logs/modules', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                const data = await response.json();
                updateResponseDetails({
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                });

                if (response.ok) {
                    updateStatus('✅ API调用成功', 'success');
                    updateModulesList(data.modules, false);
                } else {
                    // 模拟前端错误处理逻辑
                    const defaultModules = [
                        'SYSTEM', 'AUTH', 'USER', 'API', 'DATABASE', 
                        'CACHE', 'EMAIL', 'FILE', 'SYNC', 'ADMIN'
                    ];
                    
                    if (response.status === 401) {
                        updateStatus('⚠️ 认证失败，使用默认模块列表', 'warning');
                    } else if (response.status === 404) {
                        updateStatus('⚠️ 接口不存在，使用默认模块列表', 'warning');
                    } else {
                        updateStatus(`❌ API调用失败 (${response.status})，使用默认模块列表`, 'error');
                    }
                    
                    updateModulesList(defaultModules, true);
                }

            } catch (error) {
                console.error('API调用失败:', error);
                updateResponseDetails({
                    error: error.message,
                    type: 'Network Error'
                });
                
                // 网络错误时的降级处理
                const defaultModules = [
                    'SYSTEM', 'AUTH', 'USER', 'API', 'DATABASE', 
                    'CACHE', 'EMAIL', 'FILE', 'SYNC', 'ADMIN'
                ];
                
                updateStatus('❌ 网络错误，使用默认模块列表', 'error');
                updateModulesList(defaultModules, true);
            } finally {
                isLoading = false;
                document.getElementById('testBtn').disabled = false;
            }
        }

        async function testWithAuth() {
            updateStatus('认证测试功能暂未实现', 'warning');
        }

        function clearResults() {
            updateStatus('准备就绪');
            updateModulesList([]);
            updateResponseDetails('等待API调用...');
        }

        // 页面加载时自动运行测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                testLogModulesAPI();
            }, 1000);
        });
    </script>
</body>
</html>
