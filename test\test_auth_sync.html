<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证状态同步测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #ecf0f1;
        }
        .success {
            color: #27ae60;
            background: #d5f4e6;
        }
        .error {
            color: #e74c3c;
            background: #fdf2f2;
        }
        .warning {
            color: #f39c12;
            background: #fef9e7;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .test-result {
            font-weight: bold;
        }
        .test-result.pass {
            color: #27ae60;
        }
        .test-result.fail {
            color: #e74c3c;
        }
        .test-result.pending {
            color: #f39c12;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>认证状态同步测试</h1>
        
        <div class="card">
            <h2>测试控制</h2>
            <button onclick="runAllTests()">运行所有测试</button>
            <button onclick="clearResults()">清除结果</button>
            <div class="status" id="overallStatus">准备运行测试</div>
        </div>

        <div class="card">
            <h2>认证状态检查</h2>
            <div class="test-item">
                <span>localStorage中的access_token</span>
                <span class="test-result pending" id="tokenTest">待测试</span>
            </div>
            <div class="test-item">
                <span>localStorage中的refresh_token</span>
                <span class="test-result pending" id="refreshTokenTest">待测试</span>
            </div>
            <div class="test-item">
                <span>/api/auth/me 接口调用</span>
                <span class="test-result pending" id="meApiTest">待测试</span>
            </div>
            <div class="test-item">
                <span>/api/auth/check 接口调用</span>
                <span class="test-result pending" id="checkApiTest">待测试</span>
            </div>
            <div class="test-item">
                <span>/api/system/logs/modules 接口调用</span>
                <span class="test-result pending" id="modulesApiTest">待测试</span>
            </div>
            <div class="test-item">
                <span>/api/system/logs 接口调用</span>
                <span class="test-result pending" id="logsApiTest">待测试</span>
            </div>
        </div>

        <div class="card">
            <h2>测试结果详情</h2>
            <pre id="testDetails">等待测试结果...</pre>
        </div>

        <div class="card">
            <h2>修复建议</h2>
            <div id="suggestions">
                <p><strong>如果测试失败，请尝试以下步骤：</strong></p>
                <ol>
                    <li>确认已经登录管理员账户</li>
                    <li>检查浏览器控制台是否有错误信息</li>
                    <li>尝试刷新页面重新登录</li>
                    <li>清除浏览器缓存和localStorage</li>
                    <li>检查后端服务是否正常运行</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        let testResults = {};

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('overallStatus');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function updateTestResult(testId, result, details = '') {
            const element = document.getElementById(testId);
            element.textContent = result === 'pass' ? '✅ 通过' : result === 'fail' ? '❌ 失败' : '⏳ 测试中';
            element.className = `test-result ${result}`;
            testResults[testId] = { result, details };
        }

        function updateTestDetails() {
            const details = document.getElementById('testDetails');
            let output = '';
            for (const [testId, data] of Object.entries(testResults)) {
                output += `${testId}: ${data.result}\n`;
                if (data.details) {
                    output += `  详情: ${data.details}\n`;
                }
                output += '\n';
            }
            details.textContent = output || '等待测试结果...';
        }

        async function testLocalStorageTokens() {
            updateTestResult('tokenTest', 'pending');
            updateTestResult('refreshTokenTest', 'pending');

            const accessToken = localStorage.getItem('access_token');
            const refreshToken = localStorage.getItem('refresh_token');

            updateTestResult('tokenTest', accessToken ? 'pass' : 'fail', 
                accessToken ? `Token存在: ${accessToken.substring(0, 20)}...` : '未找到access_token');
            
            updateTestResult('refreshTokenTest', refreshToken ? 'pass' : 'fail',
                refreshToken ? `Refresh token存在: ${refreshToken.substring(0, 20)}...` : '未找到refresh_token');
        }

        async function testMeApi() {
            updateTestResult('meApiTest', 'pending');
            
            try {
                const token = localStorage.getItem('access_token');
                if (!token) {
                    updateTestResult('meApiTest', 'fail', '没有access_token');
                    return;
                }

                const response = await fetch('/api/auth/me', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    updateTestResult('meApiTest', 'pass', `用户: ${data.username}, 角色: ${data.role}`);
                } else {
                    updateTestResult('meApiTest', 'fail', `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                updateTestResult('meApiTest', 'fail', `网络错误: ${error.message}`);
            }
        }

        async function testCheckApi() {
            updateTestResult('checkApiTest', 'pending');
            
            try {
                const token = localStorage.getItem('access_token');
                if (!token) {
                    updateTestResult('checkApiTest', 'fail', '没有access_token');
                    return;
                }

                const response = await fetch('/api/auth/check', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    updateTestResult('checkApiTest', 'pass', `Token有效: ${data.message}`);
                } else {
                    updateTestResult('checkApiTest', 'fail', `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                updateTestResult('checkApiTest', 'fail', `网络错误: ${error.message}`);
            }
        }

        async function testModulesApi() {
            updateTestResult('modulesApiTest', 'pending');
            
            try {
                const token = localStorage.getItem('access_token');
                if (!token) {
                    updateTestResult('modulesApiTest', 'fail', '没有access_token');
                    return;
                }

                const response = await fetch('/api/system/logs/modules', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    updateTestResult('modulesApiTest', 'pass', `获取到${data.modules?.length || 0}个模块`);
                } else {
                    updateTestResult('modulesApiTest', 'fail', `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                updateTestResult('modulesApiTest', 'fail', `网络错误: ${error.message}`);
            }
        }

        async function testLogsApi() {
            updateTestResult('logsApiTest', 'pending');
            
            try {
                const token = localStorage.getItem('access_token');
                if (!token) {
                    updateTestResult('logsApiTest', 'fail', '没有access_token');
                    return;
                }

                const response = await fetch('/api/system/logs?page=1&page_size=5', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    updateTestResult('logsApiTest', 'pass', `获取到${data.items?.length || 0}条日志`);
                } else {
                    updateTestResult('logsApiTest', 'fail', `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                updateTestResult('logsApiTest', 'fail', `网络错误: ${error.message}`);
            }
        }

        async function runAllTests() {
            updateStatus('正在运行测试...', 'warning');
            testResults = {};

            await testLocalStorageTokens();
            await testMeApi();
            await testCheckApi();
            await testModulesApi();
            await testLogsApi();

            updateTestDetails();

            // 检查总体结果
            const failedTests = Object.values(testResults).filter(r => r.result === 'fail').length;
            const passedTests = Object.values(testResults).filter(r => r.result === 'pass').length;

            if (failedTests === 0) {
                updateStatus(`✅ 所有测试通过 (${passedTests}/${Object.keys(testResults).length})`, 'success');
            } else {
                updateStatus(`❌ ${failedTests} 个测试失败，${passedTests} 个测试通过`, 'error');
            }
        }

        function clearResults() {
            testResults = {};
            const testElements = document.querySelectorAll('.test-result');
            testElements.forEach(el => {
                el.textContent = '待测试';
                el.className = 'test-result pending';
            });
            document.getElementById('testDetails').textContent = '等待测试结果...';
            updateStatus('准备运行测试');
        }

        // 页面加载时自动运行测试
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
