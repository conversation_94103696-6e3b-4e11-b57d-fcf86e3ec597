<template>
  <div class="tabs-container">
    <router-view v-slot="{ Component }">
      <keep-alive :include="tabsStore.cachedViewNames">
        <component
          :is="Component"
          v-if="tabsStore.activeTab && Component"
          :key="tabsStore.activeTab.id"
        />
      </keep-alive>
    </router-view>

    <!-- 无标签时的占位内容 -->
    <div v-if="!tabsStore.activeTab" class="empty-tabs">
      <el-empty
        description="暂无打开的页面"
        :image-size="120"
      >
        <el-button type="primary" @click="goToDashboard">
          前往首页
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useTabsStore } from '@/stores/tabs'

const router = useRouter()
const tabsStore = useTabsStore()

// 前往首页
const goToDashboard = () => {
  router.push('/dashboard')
}
</script>

<style lang="scss" scoped>
.tabs-container {
  height: 100%;
  width: 100%;
  overflow: auto; // 允许滚动
  position: relative;

  .empty-tabs {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 确保路由视图和组件占满容器
:deep(.router-view) {
  height: 100%;
  width: 100%;
}

// 确保动态组件占满容器
:deep(.router-view > *) {
  height: 100%;
  width: 100%;
}
</style>
