<template>
  <div class="settings-page page-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>系统设置</h1>
      <p>配置系统参数、安全选项和个性化设置</p>
    </div>

    <!-- 设置容器 -->
    <div class="settings-container">
      <el-tabs v-model="activeTab" type="border-card" class="settings-tabs modern-tabs">
        <!-- 基础配置 -->
        <el-tab-pane label="基础配置" name="system">
          <el-card class="settings-card">
            <template #header>
              <div class="card-header">
                <span>基础配置</span>
              </div>
            </template>
            
            <el-form :model="systemForm" :rules="systemRules" ref="systemFormRef" label-width="120px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="网站名称" prop="siteName">
                    <el-input v-model="systemForm.siteName" placeholder="请输入网站名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="网站版本" prop="siteVersion">
                    <el-input v-model="systemForm.siteVersion" placeholder="请输入版本号" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="网站描述" prop="siteDescription">
                <el-input
                  v-model="systemForm.siteDescription"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入网站描述"
                />
              </el-form-item>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="网站Logo" prop="siteLogo">
                    <el-upload
                      class="logo-uploader"
                      :action="uploadUrl"
                      :headers="uploadHeaders"
                      :show-file-list="false"
                      :on-success="handleLogoSuccess"
                      :before-upload="beforeLogoUpload"
                    >
                      <img v-if="systemForm.siteLogo" :src="systemForm.siteLogo" class="logo" />
                      <el-icon v-else class="logo-uploader-icon"><Plus /></el-icon>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="网站图标" prop="siteFavicon">
                    <el-upload
                      class="favicon-uploader"
                      :action="uploadUrl"
                      :headers="uploadHeaders"
                      :show-file-list="false"
                      :on-success="handleFaviconSuccess"
                      :before-upload="beforeFaviconUpload"
                    >
                      <img v-if="systemForm.siteFavicon" :src="systemForm.siteFavicon" class="favicon" />
                      <el-icon v-else class="favicon-uploader-icon"><Plus /></el-icon>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="时区设置" prop="timezone">
                    <el-select v-model="systemForm.timezone" placeholder="请选择时区" filterable>
                      <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai" />
                      <el-option label="东京时间 (UTC+9)" value="Asia/Tokyo" />
                      <el-option label="纽约时间 (UTC-5)" value="America/New_York" />
                      <el-option label="伦敦时间 (UTC+0)" value="Europe/London" />
                      <el-option label="悉尼时间 (UTC+10)" value="Australia/Sydney" />
                    </el-select>
                  </el-form-item>
                </el-col>

              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="文件上传限制" prop="maxFileSize">
                    <el-input-number
                      v-model="systemForm.maxFileSize"
                      :min="1"
                      :max="100"
                      controls-position="right"
                    />
                    <span class="form-tip">MB</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="分页大小" prop="defaultPageSize">
                    <el-input-number
                      v-model="systemForm.defaultPageSize"
                      :min="10"
                      :max="100"
                      :step="10"
                      controls-position="right"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="维护模式">
                    <el-switch v-model="systemForm.maintenanceMode" />
                    <span class="form-tip">开启后，普通用户将无法访问系统</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="调试模式">
                    <el-switch v-model="systemForm.debugMode" />
                    <span class="form-tip">开启后显示详细错误信息</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="维护公告" prop="maintenanceMessage" v-if="systemForm.maintenanceMode">
                <el-input
                  v-model="systemForm.maintenanceMessage"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入维护期间显示的公告内容"
                />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="saveSystemSettings" :loading="systemLoading">
                  保存设置
                </el-button>
                <el-button @click="resetSystemForm">重置</el-button>
                <el-button type="info" @click="exportSystemSettings">导出配置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-tab-pane>
        
        <!-- 安全设置 -->
        <el-tab-pane label="安全设置" name="security">
          <el-card class="settings-card">
            <template #header>
              <div class="card-header">
                <span>安全策略</span>
              </div>
            </template>
            
            <el-form :model="securityForm" :rules="securityRules" ref="securityFormRef" label-width="120px">
              <el-divider content-position="left">密码策略</el-divider>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="密码最小长度" prop="minPasswordLength">
                    <el-input-number
                      v-model="securityForm.minPasswordLength"
                      :min="6"
                      :max="20"
                      controls-position="right"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="密码有效期" prop="passwordExpireDays">
                    <el-input-number
                      v-model="securityForm.passwordExpireDays"
                      :min="0"
                      :max="365"
                      controls-position="right"
                    />
                    <span class="form-tip">天（0表示永不过期）</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="密码复杂度要求">
                    <el-checkbox-group v-model="securityForm.passwordRequirements">
                      <el-checkbox label="uppercase">包含大写字母</el-checkbox>
                      <el-checkbox label="lowercase">包含小写字母</el-checkbox>
                      <el-checkbox label="numbers">包含数字</el-checkbox>
                      <el-checkbox label="special">包含特殊字符</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="禁止重复历史密码">
                    <el-input-number
                      v-model="securityForm.passwordHistoryCount"
                      :min="0"
                      :max="10"
                      controls-position="right"
                    />
                    <span class="form-tip">个（0表示不限制）</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-divider content-position="left">登录安全</el-divider>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="登录失败限制" prop="maxLoginAttempts">
                    <el-input-number
                      v-model="securityForm.maxLoginAttempts"
                      :min="3"
                      :max="10"
                      controls-position="right"
                    />
                    <span class="form-tip">连续失败次数</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="账户锁定时间" prop="lockoutDuration">
                    <el-input-number
                      v-model="securityForm.lockoutDuration"
                      :min="5"
                      :max="1440"
                      controls-position="right"
                    />
                    <span class="form-tip">分钟</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="会话超时时间" prop="sessionTimeout">
                    <el-select v-model="securityForm.sessionTimeout" placeholder="请选择">
                      <el-option label="15分钟" :value="15" />
                      <el-option label="30分钟" :value="30" />
                      <el-option label="1小时" :value="60" />
                      <el-option label="2小时" :value="120" />
                      <el-option label="4小时" :value="240" />
                      <el-option label="8小时" :value="480" />
                      <el-option label="24小时" :value="1440" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="同时在线设备数" prop="maxConcurrentSessions">
                    <el-input-number
                      v-model="securityForm.maxConcurrentSessions"
                      :min="1"
                      :max="10"
                      controls-position="right"
                    />
                    <span class="form-tip">个设备</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-divider content-position="left">访问控制</el-divider>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="强制HTTPS">
                    <el-switch v-model="securityForm.forceHttps" />
                    <span class="form-tip">强制使用HTTPS协议访问</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="启用双因子认证">
                    <el-switch v-model="securityForm.enableTwoFactor" />
                    <span class="form-tip">提高账户安全性</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="IP白名单" prop="ipWhitelist">
                <el-input
                  v-model="securityForm.ipWhitelist"
                  type="textarea"
                  :rows="3"
                  placeholder="每行一个IP地址或IP段，例如：*********** 或 ***********/24"
                />
              </el-form-item>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="API访问限流">
                    <el-switch v-model="securityForm.enableRateLimit" />
                    <span class="form-tip">防止API滥用</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12" v-if="securityForm.enableRateLimit">
                  <el-form-item label="每分钟请求限制" prop="rateLimitPerMinute">
                    <el-input-number
                      v-model="securityForm.rateLimitPerMinute"
                      :min="10"
                      :max="1000"
                      controls-position="right"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item>
                <el-button type="primary" @click="saveSecuritySettings" :loading="securityLoading">
                  保存设置
                </el-button>
                <el-button @click="resetSecurityForm">重置</el-button>
                <el-button type="warning" @click="testSecuritySettings">测试配置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-tab-pane>
        
        <!-- 通知设置 -->
        <el-tab-pane label="通知设置" name="notification">
          <el-card class="settings-card">
            <template #header>
              <div class="card-header">
                <span>通知配置</span>
              </div>
            </template>
            
            <el-form :model="notificationForm" ref="notificationFormRef" label-width="120px">
              <el-divider content-position="left">邮件通知</el-divider>

              <el-form-item label="启用邮件通知">
                <el-switch v-model="notificationForm.emailEnabled" />
                <span class="form-tip">系统事件邮件提醒</span>
              </el-form-item>

              <template v-if="notificationForm.emailEnabled">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="SMTP服务器" prop="smtpHost">
                      <el-input v-model="notificationForm.smtpHost" placeholder="smtp.example.com" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="SMTP端口" prop="smtpPort">
                      <el-input-number v-model="notificationForm.smtpPort" :min="1" :max="65535" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="发件人邮箱" prop="senderEmail">
                      <el-input v-model="notificationForm.senderEmail" placeholder="<EMAIL>" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="发件人名称" prop="senderName">
                      <el-input v-model="notificationForm.senderName" placeholder="系统通知" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="SMTP用户名" prop="smtpUsername">
                      <el-input v-model="notificationForm.smtpUsername" placeholder="SMTP登录用户名" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="SMTP密码" prop="smtpPassword">
                      <el-input v-model="notificationForm.smtpPassword" type="password" placeholder="SMTP登录密码" show-password />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="启用TLS">
                      <el-switch v-model="notificationForm.smtpTls" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="启用SSL">
                      <el-switch v-model="notificationForm.smtpSsl" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-form-item>
                  <el-button type="info" @click="testEmailSettings">测试邮件发送</el-button>
                </el-form-item>
              </template>

              <el-divider content-position="left">其他通知渠道</el-divider>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="系统消息">
                    <el-switch v-model="notificationForm.systemMessageEnabled" />
                    <span class="form-tip">站内消息通知</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="浏览器通知">
                    <el-switch v-model="notificationForm.browserNotificationEnabled" />
                    <span class="form-tip">浏览器桌面通知</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="短信通知">
                    <el-switch v-model="notificationForm.smsEnabled" />
                    <span class="form-tip">重要事件短信提醒</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="钉钉通知">
                    <el-switch v-model="notificationForm.dingTalkEnabled" />
                    <span class="form-tip">钉钉群机器人通知</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <template v-if="notificationForm.dingTalkEnabled">
                <el-form-item label="钉钉Webhook" prop="dingTalkWebhook">
                  <el-input v-model="notificationForm.dingTalkWebhook" placeholder="钉钉机器人Webhook地址" />
                </el-form-item>
              </template>

              <el-divider content-position="left">通知事件配置</el-divider>

              <el-form-item label="通知事件类型">
                <el-checkbox-group v-model="notificationForm.notificationEvents">
                  <el-checkbox label="user_login">用户登录</el-checkbox>
                  <el-checkbox label="user_register">用户注册</el-checkbox>
                  <el-checkbox label="system_error">系统错误</el-checkbox>
                  <el-checkbox label="security_alert">安全警报</el-checkbox>
                  <el-checkbox label="data_sync">数据同步</el-checkbox>
                  <el-checkbox label="backup_complete">备份完成</el-checkbox>
                </el-checkbox-group>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="saveNotificationSettings" :loading="notificationLoading">
                  保存设置
                </el-button>
                <el-button @click="resetNotificationForm">重置</el-button>
                <el-button type="info" @click="previewNotificationTemplate">预览模板</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-tab-pane>
        

        <!-- 权限管理 -->
        <el-tab-pane label="权限管理" name="permission">
          <el-card class="settings-card">
            <template #header>
              <div class="card-header">
                <span>角色权限配置</span>
              </div>
            </template>

            <el-form :model="permissionForm" ref="permissionFormRef" label-width="120px">
              <el-divider content-position="left">角色管理</el-divider>

              <el-form-item label="系统角色">
                <el-table :data="permissionForm.roles" style="width: 100%" border>
                  <el-table-column prop="name" label="角色名称" width="150">
                    <template #default="{ row }">
                      <el-tag :type="getRoleTagType(row.level)">{{ row.name }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="description" label="角色描述" />
                  <el-table-column prop="level" label="权限级别" width="100" />
                  <el-table-column prop="userCount" label="用户数量" width="100" />
                  <el-table-column label="状态" width="100">
                    <template #default="{ row }">
                      <el-switch v-model="row.enabled" @change="toggleRoleStatus(row)" />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150">
                    <template #default="{ row }">
                      <el-button type="primary" size="small" @click="editRole(row)">编辑</el-button>
                      <el-button type="danger" size="small" @click="deleteRole(row)" :disabled="row.level >= 9">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>

                <el-button type="primary" @click="addNewRole" style="margin-top: 10px;">
                  <el-icon><Plus /></el-icon>
                  添加角色
                </el-button>
              </el-form-item>

              <el-divider content-position="left">功能权限</el-divider>

              <el-form-item label="选择角色">
                <el-select v-model="permissionForm.selectedRole" placeholder="请选择要配置的角色" @change="loadRolePermissions">
                  <el-option
                    v-for="role in permissionForm.roles.filter(r => r.enabled)"
                    :key="role.id"
                    :label="role.name"
                    :value="role.id"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="功能权限" v-if="permissionForm.selectedRole">
                <el-tree
                  ref="permissionTreeRef"
                  :data="permissionForm.permissionTree"
                  :props="{ children: 'children', label: 'label' }"
                  show-checkbox
                  node-key="id"
                  :default-checked-keys="permissionForm.checkedPermissions"
                  @check="handlePermissionCheck"
                >
                  <template #default="{ node, data }">
                    <span class="permission-node">
                      <el-icon v-if="data.icon"><component :is="data.icon" /></el-icon>
                      {{ data.label }}
                      <el-tag v-if="data.type" size="small" :type="data.type === 'module' ? 'primary' : 'info'">
                        {{ data.type === 'module' ? '模块' : '功能' }}
                      </el-tag>
                    </span>
                  </template>
                </el-tree>
              </el-form-item>

              <el-divider content-position="left">API权限</el-divider>

              <el-form-item label="API访问控制" v-if="permissionForm.selectedRole">
                <el-table :data="permissionForm.apiPermissions" style="width: 100%" border size="small">
                  <el-table-column prop="path" label="API路径" />
                  <el-table-column prop="method" label="请求方法" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getMethodTagType(row.method)">{{ row.method }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="description" label="功能描述" />
                  <el-table-column label="访问权限" width="100">
                    <template #default="{ row }">
                      <el-switch v-model="row.allowed" @change="updateApiPermission(row)" />
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="savePermissionSettings" :loading="permissionLoading">
                  保存权限配置
                </el-button>
                <el-button @click="resetPermissionForm">重置</el-button>
                <el-button type="info" @click="exportPermissionConfig">导出权限配置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-tab-pane>





        <!-- 接口管理 -->
        <el-tab-pane label="接口管理" name="api">
          <el-card class="settings-card">
            <template #header>
              <div class="card-header">
                <span>API接口管理</span>
              </div>
            </template>

            <el-form :model="apiForm" ref="apiFormRef" label-width="120px">
              <el-divider content-position="left">API配置</el-divider>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="API版本" prop="apiVersion">
                    <el-input v-model="apiForm.apiVersion" placeholder="v1.0.0" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="API前缀" prop="apiPrefix">
                    <el-input v-model="apiForm.apiPrefix" placeholder="/api/v1" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="启用API文档">
                    <el-switch v-model="apiForm.enableDocs" />
                    <span class="form-tip">Swagger/OpenAPI文档</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="启用CORS">
                    <el-switch v-model="apiForm.enableCors" />
                    <span class="form-tip">跨域资源共享</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="允许的域名" v-if="apiForm.enableCors">
                <el-input
                  v-model="apiForm.allowedOrigins"
                  type="textarea"
                  :rows="3"
                  placeholder="每行一个域名，例如：https://example.com"
                />
              </el-form-item>

              <el-divider content-position="left">第三方服务集成</el-divider>

              <el-table :data="apiForm.thirdPartyServices" style="width: 100%" border>
                <el-table-column prop="name" label="服务名称" width="150" />
                <el-table-column prop="description" label="服务描述" />
                <el-table-column prop="endpoint" label="接口地址" />
                <el-table-column label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.enabled ? 'success' : 'info'">
                      {{ row.enabled ? '已启用' : '已禁用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                  <template #default="{ row }">
                    <el-button type="primary" size="small" @click="configureService(row)">配置</el-button>
                    <el-button type="info" size="small" @click="testService(row)">测试</el-button>
                    <el-switch v-model="row.enabled" size="small" @change="toggleService(row)" />
                  </template>
                </el-table-column>
              </el-table>

              <el-button type="primary" @click="addThirdPartyService" style="margin-top: 10px;">
                <el-icon><Plus /></el-icon>
                添加服务
              </el-button>

              <el-divider content-position="left">Webhook配置</el-divider>

              <el-table :data="apiForm.webhooks" style="width: 100%" border>
                <el-table-column prop="name" label="Webhook名称" width="150" />
                <el-table-column prop="url" label="回调地址" />
                <el-table-column prop="events" label="触发事件" width="200">
                  <template #default="{ row }">
                    <el-tag v-for="event in row.events" :key="event" size="small" style="margin-right: 5px;">
                      {{ event }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.enabled ? 'success' : 'info'">
                      {{ row.enabled ? '已启用' : '已禁用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                  <template #default="{ row }">
                    <el-button type="primary" size="small" @click="editWebhook(row)">编辑</el-button>
                    <el-button type="info" size="small" @click="testWebhook(row)">测试</el-button>
                    <el-button type="danger" size="small" @click="deleteWebhook(row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>

              <el-button type="primary" @click="addWebhook" style="margin-top: 10px;">
                <el-icon><Plus /></el-icon>
                添加Webhook
              </el-button>

              <el-divider content-position="left">API监控</el-divider>

              <el-row :gutter="20">
                <el-col :span="6">
                  <el-statistic title="今日请求数" :value="apiForm.apiStats.todayRequests" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="平均响应时间" :value="apiForm.apiStats.avgResponseTime" suffix="ms" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="成功率" :value="apiForm.apiStats.successRate" suffix="%" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="错误数" :value="apiForm.apiStats.errorCount" />
                </el-col>
              </el-row>

              <el-row :gutter="20" style="margin-top: 20px;">
                <el-col :span="12">
                  <el-form-item label="启用API监控">
                    <el-switch v-model="apiForm.enableApiMonitoring" />
                    <span class="form-tip">记录API调用统计</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="监控数据保留">
                    <el-select v-model="apiForm.monitoringRetentionDays" placeholder="选择保留期">
                      <el-option label="7天" :value="7" />
                      <el-option label="30天" :value="30" />
                      <el-option label="90天" :value="90" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item>
                <el-button type="primary" @click="saveApiSettings" :loading="apiLoading">
                  保存接口配置
                </el-button>
                <el-button @click="resetApiForm">重置</el-button>
                <el-button type="info" @click="viewApiDocs">查看API文档</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-tab-pane>

        <!-- 主题外观 -->
        <el-tab-pane label="主题外观" name="theme">
          <el-card class="settings-card">
            <template #header>
              <div class="card-header">
                <span>界面主题设置</span>
              </div>
            </template>

            <el-form :model="themeForm" ref="themeFormRef" label-width="120px">
              <el-divider content-position="left">主题配置</el-divider>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="主题模式" prop="themeMode">
                    <el-radio-group v-model="themeForm.themeMode" @change="changeThemeMode">
                      <el-radio-button label="light">浅色模式</el-radio-button>
                      <el-radio-button label="dark">深色模式</el-radio-button>
                      <el-radio-button label="auto">跟随系统</el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="主题色彩" prop="primaryColor">
                    <el-color-picker v-model="themeForm.primaryColor" @change="changePrimaryColor" />
                    <span class="form-tip">系统主色调</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="预设主题">
                <div class="theme-presets">
                  <div
                    v-for="preset in themeForm.presetThemes"
                    :key="preset.name"
                    class="theme-preset-item"
                    :class="{ active: themeForm.selectedPreset === preset.name }"
                    @click="applyPresetTheme(preset)"
                  >
                    <div class="theme-preview">
                      <div class="color-bar" :style="{ backgroundColor: preset.primaryColor }"></div>
                      <div class="color-bar" :style="{ backgroundColor: preset.secondaryColor }"></div>
                      <div class="color-bar" :style="{ backgroundColor: preset.backgroundColor }"></div>
                    </div>
                    <span class="theme-name">{{ preset.label }}</span>
                  </div>
                </div>
              </el-form-item>

              <el-divider content-position="left">布局设置</el-divider>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="侧边栏宽度" prop="sidebarWidth">
                    <el-slider v-model="themeForm.sidebarWidth" :min="200" :max="300" :step="10" />
                    <span class="form-tip">{{ themeForm.sidebarWidth }}px</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="内容区域宽度" prop="contentMaxWidth">
                    <el-select v-model="themeForm.contentMaxWidth" placeholder="选择内容宽度">
                      <el-option label="自适应" value="auto" />
                      <el-option label="1200px" value="1200px" />
                      <el-option label="1400px" value="1400px" />
                      <el-option label="1600px" value="1600px" />
                      <el-option label="全屏" value="100%" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="紧凑模式">
                    <el-switch v-model="themeForm.compactMode" @change="toggleCompactMode" />
                    <span class="form-tip">减少间距和内边距</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="固定头部">
                    <el-switch v-model="themeForm.fixedHeader" @change="toggleFixedHeader" />
                    <span class="form-tip">头部导航固定在顶部</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="显示面包屑">
                    <el-switch v-model="themeForm.showBreadcrumb" />
                    <span class="form-tip">显示页面导航路径</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="显示页脚">
                    <el-switch v-model="themeForm.showFooter" />
                    <span class="form-tip">显示页面底部信息</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="数字格式" prop="numberFormat">
                    <el-select v-model="themeForm.numberFormat">
                      <el-option label="1,234.56" value="en" />
                      <el-option label="1 234,56" value="fr" />
                      <el-option label="1.234,56" value="de" />
                      <el-option label="1,234.56" value="zh" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-divider content-position="left">登录页面自定义</el-divider>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="左侧装饰图片">
                    <div class="image-upload-section">
                      <div class="image-preview" v-if="loginPageForm.decorativeImage">
                        <img :src="loginPageForm.decorativeImage" alt="装饰图片预览" />
                        <div class="image-actions">
                          <el-button size="small" type="danger" @click="removeDecorativeImage">
                            <el-icon><Delete /></el-icon>
                            移除
                          </el-button>
                        </div>
                      </div>
                      <div v-else class="upload-placeholder">
                        <el-icon size="50" color="#ccc"><Picture /></el-icon>
                        <p>暂无装饰图片</p>
                      </div>
                      <div class="upload-controls">
                        <el-upload
                          ref="decorativeUploadRef"
                          :auto-upload="false"
                          :show-file-list="false"
                          accept="image/jpeg,image/jpg,image/png,image/webp"
                          :before-upload="beforeDecorativeUpload"
                          :on-change="handleDecorativeImageChange"
                        >
                          <el-button type="primary">
                            <el-icon><Upload /></el-icon>
                            选择图片
                          </el-button>
                        </el-upload>
                        <span class="form-tip">支持 JPG、PNG、WebP 格式，建议尺寸 400x300px，文件大小不超过 2MB</span>
                      </div>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="背景图片">
                    <div class="image-upload-section">
                      <div class="image-preview background-preview" v-if="loginPageForm.backgroundImage">
                        <img :src="loginPageForm.backgroundImage" alt="背景图片预览" />
                        <div class="image-actions">
                          <el-button size="small" type="danger" @click="removeBackgroundImage">
                            <el-icon><Delete /></el-icon>
                            移除
                          </el-button>
                        </div>
                      </div>
                      <div v-else class="upload-placeholder background-placeholder">
                        <el-icon size="50" color="#ccc"><Picture /></el-icon>
                        <p>使用默认渐变背景</p>
                      </div>
                      <div class="upload-controls">
                        <el-upload
                          ref="backgroundUploadRef"
                          :auto-upload="false"
                          :show-file-list="false"
                          accept="image/jpeg,image/jpg,image/png,image/webp"
                          :before-upload="beforeBackgroundUpload"
                          :on-change="handleBackgroundImageChange"
                        >
                          <el-button type="primary">
                            <el-icon><Upload /></el-icon>
                            选择图片
                          </el-button>
                        </el-upload>
                        <span class="form-tip">支持 JPG、PNG、WebP 格式，建议尺寸 1920x1080px，文件大小不超过 5MB</span>
                      </div>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item>
                    <el-button type="success" @click="saveLoginPageSettings" :loading="loginPageSaving">
                      <el-icon><Check /></el-icon>
                      保存登录页面设置
                    </el-button>
                    <el-button @click="resetLoginPageSettings">
                      <el-icon><Refresh /></el-icon>
                      重置为默认
                    </el-button>
                    <el-button type="info" @click="previewLoginPage">
                      <el-icon><View /></el-icon>
                      预览效果
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-divider content-position="left">个性化设置</el-divider>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="动画效果">
                    <el-switch v-model="themeForm.enableAnimations" @change="toggleAnimations" />
                    <span class="form-tip">页面切换和交互动画</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="音效提示">
                    <el-switch v-model="themeForm.enableSounds" />
                    <span class="form-tip">操作成功/失败音效</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="字体大小" prop="fontSize">
                    <el-radio-group v-model="themeForm.fontSize" @change="changeFontSize">
                      <el-radio-button label="small">小</el-radio-button>
                      <el-radio-button label="medium">中</el-radio-button>
                      <el-radio-button label="large">大</el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="圆角风格" prop="borderRadius">
                    <el-slider v-model="themeForm.borderRadius" :min="0" :max="16" :step="2" />
                    <span class="form-tip">{{ themeForm.borderRadius }}px</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="自定义CSS">
                <el-input
                  v-model="themeForm.customCss"
                  type="textarea"
                  :rows="6"
                  placeholder="/* 在这里输入自定义CSS样式 */&#10;.custom-style {&#10;  color: #409eff;&#10;}"
                />
                <div style="margin-top: 10px;">
                  <el-button type="info" @click="previewCustomCss">预览效果</el-button>
                  <el-button type="warning" @click="resetCustomCss">重置样式</el-button>
                </div>
              </el-form-item>

              <el-divider content-position="left">主题管理</el-divider>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="保存主题">
                    <el-input v-model="themeForm.saveThemeName" placeholder="输入主题名称" />
                    <el-button type="primary" @click="saveCustomTheme" style="margin-top: 10px; width: 100%;">
                      保存当前主题
                    </el-button>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="导入主题">
                    <el-upload
                      :action="uploadUrl"
                      :headers="uploadHeaders"
                      :on-success="handleThemeImportSuccess"
                      :before-upload="beforeThemeUpload"
                      :show-file-list="false"
                    >
                      <el-button type="success" style="width: 100%;">
                        <el-icon><Upload /></el-icon>
                        导入主题文件
                      </el-button>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="导出主题">
                    <el-button type="info" @click="exportCurrentTheme" style="width: 100%;">
                      <el-icon><Download /></el-icon>
                      导出当前主题
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="我的主题" v-if="themeForm.customThemes.length > 0">
                <div class="custom-themes">
                  <div
                    v-for="theme in themeForm.customThemes"
                    :key="theme.id"
                    class="custom-theme-item"
                  >
                    <span class="theme-name">{{ theme.name }}</span>
                    <div class="theme-actions">
                      <el-button type="primary" size="small" @click="applyCustomTheme(theme)">应用</el-button>
                      <el-button type="info" size="small" @click="editCustomTheme(theme)">编辑</el-button>
                      <el-button type="danger" size="small" @click="deleteCustomTheme(theme)">删除</el-button>
                    </div>
                  </div>
                </div>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="saveThemeSettings" :loading="themeLoading">
                  保存主题设置
                </el-button>
                <el-button @click="resetThemeForm">重置</el-button>
                <el-button type="warning" @click="resetToDefault">恢复默认</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-tab-pane>

        <!-- AI配置 -->
        <el-tab-pane label="AI配置" name="ai">
          <el-card class="settings-card">
            <template #header>
              <div class="card-header">
                <span>AI模型配置</span>
                <el-button type="primary" size="small" @click="addAIModel">
                  <el-icon><Plus /></el-icon>
                  添加模型
                </el-button>
              </div>
            </template>

            <el-form :model="aiForm" ref="aiFormRef" label-width="120px">
              <el-divider content-position="left">全局设置</el-divider>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="启用AI功能">
                    <el-switch v-model="aiForm.aiEnabled" @change="toggleAIFeature" />
                    <span class="form-tip">开启后可使用AI相关功能</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="默认AI模型" v-if="aiForm.aiEnabled">
                    <el-select v-model="aiForm.defaultModel" placeholder="选择默认模型">
                      <el-option
                        v-for="model in (aiForm.models || []).filter(m => m.enabled)"
                        :key="model.id"
                        :label="model.display_name"
                        :value="model.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-divider content-position="left">AI模型列表</el-divider>

              <el-table :data="aiForm.models || []" style="width: 100%" border>
                <el-table-column prop="display_name" label="模型名称" width="200">
                  <template #default="{ row }">
                    <div class="model-info">
                      <el-tag :type="getProviderTagType(row.provider)" size="small">
                        {{ getProviderName(row.provider) }}
                      </el-tag>
                      <span class="model-name">{{ row.display_name }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" />
                <el-table-column prop="model_name" label="模型标识" width="150" />
                <el-table-column label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.enabled ? 'success' : 'info'">
                      {{ row.enabled ? '已启用' : '已禁用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="默认" width="80">
                  <template #default="{ row }">
                    <el-tag v-if="row.is_default" type="warning" size="small">默认</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="250">
                  <template #default="{ row }">
                    <el-button type="primary" size="small" @click="editAIModel(row)">配置</el-button>
                    <el-button type="info" size="small" @click="testAIModel(row)" :loading="row.testing">测试</el-button>
                    <el-switch
                      v-model="row.enabled"
                      size="small"
                      @change="toggleAIModel(row)"
                      style="margin-left: 8px;"
                    />
                    <el-button type="danger" size="small" @click="deleteAIModel(row)" style="margin-left: 8px;">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>

              <el-form-item style="margin-top: 20px;">
                <el-button type="primary" @click="saveAISettings" :loading="aiLoading">
                  保存AI配置
                </el-button>
                <el-button @click="resetAIForm">重置</el-button>
                <el-button type="info" @click="importAIConfig">导入配置</el-button>
                <el-button type="warning" @click="exportAIConfig">导出配置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>

  <!-- AI模型配置对话框 -->
  <el-dialog
    v-model="aiModelDialog.visible"
    :title="aiModelDialog.isEdit ? '编辑AI模型' : '添加AI模型'"
    width="800px"
    @close="closeAIModelDialog"
  >
    <el-form :model="aiModelDialog.form" :rules="aiModelRules" ref="aiModelFormRef" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="服务提供商" prop="provider">
            <el-select v-model="aiModelDialog.form.provider" @change="onProviderChange" placeholder="选择AI服务商">
              <el-option-group label="国内主流">
                <el-option label="通义千问 (Qwen)" value="qwen" />
                <el-option label="DeepSeek" value="deepseek" />
                <el-option label="Kimi (Moonshot)" value="kimi" />
                <el-option label="豆包 (ByteDance)" value="doubao" />
                <el-option label="百川智能 (Baichuan)" value="baichuan" />
                <el-option label="智谱AI (ChatGLM)" value="chatglm" />
                <el-option label="文心一言 (ERNIE)" value="wenxin" />
                <el-option label="讯飞星火 (Spark)" value="spark" />
                <el-option label="MiniMax" value="minimax" />
              </el-option-group>
              <el-option-group label="国外主流">
                <el-option label="OpenAI" value="openai" />
                <el-option label="Claude (Anthropic)" value="claude" />
                <el-option label="Gemini (Google)" value="gemini" />
              </el-option-group>
              <el-option-group label="其他">
                <el-option label="自定义" value="custom" />
              </el-option-group>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预设模型" v-if="aiModelDialog.form.provider && aiModelDialog.form.provider !== 'custom'">
            <el-select v-model="aiModelDialog.selectedPreset" @change="applyPreset" placeholder="选择预设模型">
              <el-option
                v-for="preset in getProviderPresets(aiModelDialog.form.provider)"
                :key="preset.name"
                :label="preset.display_name"
                :value="preset.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="显示名称" prop="display_name">
            <el-input v-model="aiModelDialog.form.display_name" placeholder="请输入显示名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模型标识" prop="model_name">
            <el-input v-model="aiModelDialog.form.model_name" placeholder="请输入模型名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="模型描述" prop="description">
        <el-input
          v-model="aiModelDialog.form.description"
          type="textarea"
          :rows="2"
          placeholder="请输入模型描述"
        />
      </el-form-item>

      <el-form-item label="API密钥" prop="api_key">
        <el-input
          v-model="aiModelDialog.form.api_key"
          type="password"
          placeholder="请输入API密钥"
          show-password
        />
      </el-form-item>

      <el-form-item label="API端点" prop="api_endpoint">
        <el-input v-model="aiModelDialog.form.api_endpoint" placeholder="请输入API端点URL" />
      </el-form-item>

      <el-divider content-position="left">模型参数</el-divider>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="Temperature">
            <el-slider v-model="aiModelDialog.form.parameters.temperature" :min="0" :max="2" :step="0.1" />
            <span class="form-tip">{{ aiModelDialog.form.parameters.temperature }} (创造性)</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Max Tokens">
            <el-input-number
              v-model="aiModelDialog.form.parameters.max_tokens"
              :min="1"
              :max="32000"
              controls-position="right"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="Top P">
            <el-slider v-model="aiModelDialog.form.parameters.top_p" :min="0" :max="1" :step="0.01" />
            <span class="form-tip">{{ aiModelDialog.form.parameters.top_p }} (多样性)</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="超时时间(秒)">
            <el-input-number
              v-model="aiModelDialog.form.parameters.timeout"
              :min="5"
              :max="300"
              controls-position="right"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="重试次数">
            <el-input-number
              v-model="aiModelDialog.form.parameters.retry_count"
              :min="0"
              :max="5"
              controls-position="right"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="启用流式输出">
            <el-switch v-model="aiModelDialog.form.parameters.stream" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="启用模型">
            <el-switch v-model="aiModelDialog.form.enabled" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设为默认">
            <el-switch v-model="aiModelDialog.form.is_default" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <el-button @click="closeAIModelDialog">取消</el-button>
      <el-button type="info" @click="testCurrentModel" :loading="aiModelDialog.testing">测试连接</el-button>
      <el-button type="primary" @click="saveAIModel" :loading="aiModelDialog.saving">
        {{ aiModelDialog.isEdit ? '更新' : '添加' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Upload, Download, User, Setting, Document, Delete, Picture, Check, View } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/user'
import type { AIModelConfig, AIProvider, AIModelPreset, AITestRequest, AITestResult } from '@/types/settings'
import { AI_MODEL_PRESETS } from '@/types/settings'

const userStore = useUserStore()
const activeTab = ref('system')

// 上传配置
const uploadUrl = ref('/api/upload')
const uploadHeaders = reactive({
  Authorization: `Bearer ${userStore.token}`
})

// 系统配置表单
const systemForm = reactive({
  siteName: 'Wiscude Admin System',
  siteVersion: '1.0.0',
  siteDescription: '智能管理系统',
  siteLogo: '',
  siteFavicon: '',
  timezone: 'Asia/Shanghai',
  maxFileSize: 10,
  defaultPageSize: 20,
  maintenanceMode: false,
  debugMode: false,
  maintenanceMessage: ''
})

const systemRules = {
  siteName: [
    { required: true, message: '请输入网站名称', trigger: 'blur' },
    { min: 2, max: 50, message: '网站名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  siteVersion: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
    { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式不正确（如：1.0.0）', trigger: 'blur' }
  ],
  siteDescription: [
    { required: true, message: '请输入网站描述', trigger: 'blur' },
    { max: 200, message: '描述不能超过200个字符', trigger: 'blur' }
  ],
  timezone: [
    { required: true, message: '请选择时区', trigger: 'change' }
  ]
}

// 安全设置表单
const securityForm = reactive({
  minPasswordLength: 8,
  passwordExpireDays: 90,
  passwordRequirements: ['lowercase', 'numbers'],
  passwordHistoryCount: 3,
  maxLoginAttempts: 5,
  lockoutDuration: 15,
  sessionTimeout: 120,
  maxConcurrentSessions: 3,
  forceHttps: false,
  enableTwoFactor: false,
  ipWhitelist: '',
  enableRateLimit: true,
  rateLimitPerMinute: 100
})

const securityRules = {
  minPasswordLength: [
    { required: true, message: '请设置密码最小长度', trigger: 'blur' }
  ],
  maxLoginAttempts: [
    { required: true, message: '请设置登录失败限制', trigger: 'blur' }
  ]
}

// 通知设置表单
const notificationForm = reactive({
  emailEnabled: false,
  smtpHost: '',
  smtpPort: 587,
  senderEmail: '',
  senderName: '系统通知',
  smtpUsername: '',
  smtpPassword: '',
  smtpTls: true,
  smtpSsl: false,
  systemMessageEnabled: true,
  browserNotificationEnabled: false,
  smsEnabled: false,
  dingTalkEnabled: false,
  dingTalkWebhook: '',
  notificationEvents: ['system_error', 'security_alert']
})



// 权限管理表单
const permissionForm = reactive({
  roles: [
    { id: 1, name: '超级管理员', description: '拥有所有权限', level: 10, userCount: 1, enabled: true },
    { id: 2, name: '管理员', description: '拥有大部分管理权限', level: 8, userCount: 3, enabled: true },
    { id: 3, name: '编辑员', description: '拥有内容编辑权限', level: 5, userCount: 8, enabled: true },
    { id: 4, name: '查看员', description: '只能查看数据', level: 2, userCount: 15, enabled: true }
  ],
  selectedRole: null,
  permissionTree: [
    {
      id: 'user_management',
      label: '用户管理',
      icon: 'User',
      type: 'module',
      children: [
        { id: 'user_view', label: '查看用户', type: 'function' },
        { id: 'user_create', label: '创建用户', type: 'function' },
        { id: 'user_edit', label: '编辑用户', type: 'function' },
        { id: 'user_delete', label: '删除用户', type: 'function' }
      ]
    },
    {
      id: 'system_management',
      label: '系统管理',
      icon: 'Setting',
      type: 'module',
      children: [
        { id: 'system_settings', label: '系统设置', type: 'function' },
        { id: 'log_management', label: '日志管理', type: 'function' },
        { id: 'backup_management', label: '备份管理', type: 'function' }
      ]
    },
    {
      id: 'content_management',
      label: '内容管理',
      icon: 'Document',
      type: 'module',
      children: [
        { id: 'content_view', label: '查看内容', type: 'function' },
        { id: 'content_create', label: '创建内容', type: 'function' },
        { id: 'content_edit', label: '编辑内容', type: 'function' },
        { id: 'content_publish', label: '发布内容', type: 'function' }
      ]
    }
  ],
  checkedPermissions: [],
  apiPermissions: [
    { path: '/api/users', method: 'GET', description: '获取用户列表', allowed: true },
    { path: '/api/users', method: 'POST', description: '创建用户', allowed: false },
    { path: '/api/users/{id}', method: 'PUT', description: '更新用户', allowed: false },
    { path: '/api/users/{id}', method: 'DELETE', description: '删除用户', allowed: false },
    { path: '/api/settings', method: 'GET', description: '获取系统设置', allowed: true },
    { path: '/api/settings', method: 'PUT', description: '更新系统设置', allowed: false }
  ]
})





// API管理表单
const apiForm = reactive({
  apiVersion: 'v1.0.0',
  apiPrefix: '/api/v1',
  enableDocs: true,
  enableCors: true,
  allowedOrigins: 'https://localhost:3000\nhttps://localhost:5173\nhttps://example.com',
  thirdPartyServices: [
    {
      id: 1,
      name: '微信支付',
      description: '微信支付接口',
      endpoint: 'https://api.mch.weixin.qq.com',
      enabled: true
    },
    {
      id: 2,
      name: '阿里云OSS',
      description: '对象存储服务',
      endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
      enabled: true
    },
    {
      id: 3,
      name: '短信服务',
      description: '阿里云短信服务',
      endpoint: 'https://dysmsapi.aliyuncs.com',
      enabled: false
    }
  ],
  webhooks: [
    {
      id: 1,
      name: '用户注册通知',
      url: 'https://example.com/webhook/user-register',
      events: ['user_register', 'user_activate'],
      enabled: true
    },
    {
      id: 2,
      name: '系统告警',
      url: 'https://example.com/webhook/system-alert',
      events: ['system_error', 'performance_alert'],
      enabled: true
    }
  ],
  enableApiMonitoring: true,
  monitoringRetentionDays: 30,
  apiStats: {
    todayRequests: 15420,
    avgResponseTime: 156,
    successRate: 99.2,
    errorCount: 23
  }
})

// 登录页面自定义表单
const loginPageForm = reactive({
  decorativeImage: localStorage.getItem('login-decorative-image') || '',
  backgroundImage: localStorage.getItem('login-background-image') || ''
})

const loginPageSaving = ref(false)
const decorativeUploadRef = ref()
const backgroundUploadRef = ref()

// 主题外观表单
const themeForm = reactive({
  themeMode: 'light',
  primaryColor: '#409eff',
  selectedPreset: 'default',
  presetThemes: [
    { name: 'default', label: '默认蓝', primaryColor: '#409eff', secondaryColor: '#79bbff', backgroundColor: '#ffffff' },
    { name: 'green', label: '清新绿', primaryColor: '#67c23a', secondaryColor: '#95d475', backgroundColor: '#ffffff' },
    { name: 'orange', label: '活力橙', primaryColor: '#e6a23c', secondaryColor: '#ebb563', backgroundColor: '#ffffff' },
    { name: 'red', label: '热情红', primaryColor: '#f56c6c', secondaryColor: '#f78989', backgroundColor: '#ffffff' },
    { name: 'purple', label: '优雅紫', primaryColor: '#9c27b0', secondaryColor: '#ba68c8', backgroundColor: '#ffffff' },
    { name: 'dark', label: '深色主题', primaryColor: '#409eff', secondaryColor: '#79bbff', backgroundColor: '#1a1a1a' }
  ],
  sidebarWidth: 240,
  contentMaxWidth: '1200px',
  compactMode: false,
  fixedHeader: true,
  showBreadcrumb: true,
  showFooter: true,
  numberFormat: 'zh',
  enableAnimations: true,
  enableSounds: false,
  fontSize: 'medium',
  borderRadius: 4,
  customCss: '',
  saveThemeName: '',
  customThemes: [
    { id: 1, name: '我的主题1', config: {} },
    { id: 2, name: '企业主题', config: {} }
  ]
})

// AI配置表单
const aiForm = reactive({
  aiEnabled: false,
  defaultModel: '',
  models: [] as AIModelConfig[]
})

// AI模型对话框
const aiModelDialog = reactive({
  visible: false,
  isEdit: false,
  selectedPreset: '',
  testing: false,
  saving: false,
  form: {
    id: '',
    name: '',
    provider: '' as AIProvider,
    display_name: '',
    description: '',
    api_key: '',
    api_endpoint: '',
    model_name: '',
    parameters: {
      temperature: 0.7,
      max_tokens: 2000,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: false,
      timeout: 30,
      retry_count: 3
    },
    enabled: true,
    is_default: false
  } as Partial<AIModelConfig>
})

// AI模型验证规则
const aiModelRules = {
  provider: [
    { required: true, message: '请选择服务提供商', trigger: 'change' }
  ],
  display_name: [
    { required: true, message: '请输入显示名称', trigger: 'blur' },
    { min: 2, max: 50, message: '显示名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  model_name: [
    { required: true, message: '请输入模型标识', trigger: 'blur' }
  ],
  api_key: [
    { required: true, message: '请输入API密钥', trigger: 'blur' }
  ],
  api_endpoint: [
    { required: true, message: '请输入API端点', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL地址', trigger: 'blur' }
  ]
}

// 加载状态
const systemLoading = ref(false)
const securityLoading = ref(false)
const notificationLoading = ref(false)

const permissionLoading = ref(false)
const apiLoading = ref(false)
const themeLoading = ref(false)
const aiLoading = ref(false)

// 表单引用
const systemFormRef = ref()
const securityFormRef = ref()
const notificationFormRef = ref()

const permissionFormRef = ref()
const permissionTreeRef = ref()
const apiFormRef = ref()
const themeFormRef = ref()
const aiFormRef = ref()
const aiModelFormRef = ref()

// 方法
const handleLogoSuccess = (response: any) => {
  systemForm.siteLogo = response.url
  ElMessage.success('Logo上传成功')
}

const handleFaviconSuccess = (response: any) => {
  systemForm.siteFavicon = response.url
  ElMessage.success('网站图标上传成功')
}

const beforeLogoUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const beforeFaviconUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt1M = file.size / 1024 / 1024 < 1

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt1M) {
    ElMessage.error('图标大小不能超过 1MB!')
    return false
  }
  return true
}

const saveSystemSettings = async () => {
  try {
    systemLoading.value = true
    await systemFormRef.value.validate()
    // TODO: 调用API保存系统设置
    ElMessage.success('系统设置保存成功')
  } catch (error) {
    ElMessage.error('请检查表单输入')
  } finally {
    systemLoading.value = false
  }
}

const exportSystemSettings = async () => {
  try {
    // TODO: 调用API导出系统配置
    ElMessage.success('配置导出成功')
  } catch (error) {
    ElMessage.error('配置导出失败')
  }
}

const saveSecuritySettings = async () => {
  try {
    securityLoading.value = true
    await securityFormRef.value.validate()
    // TODO: 调用API保存安全设置
    ElMessage.success('安全设置保存成功')
  } catch (error) {
    ElMessage.error('请检查表单输入')
  } finally {
    securityLoading.value = false
  }
}

const testSecuritySettings = async () => {
  try {
    // TODO: 调用API测试安全配置
    ElMessage.success('安全配置测试通过')
  } catch (error) {
    ElMessage.error('安全配置测试失败')
  }
}

const saveNotificationSettings = async () => {
  try {
    notificationLoading.value = true
    // TODO: 调用API保存通知设置
    ElMessage.success('通知设置保存成功')
  } catch (error) {
    ElMessage.error('通知设置保存失败')
  } finally {
    notificationLoading.value = false
  }
}

const testEmailSettings = async () => {
  try {
    // TODO: 调用API测试邮件发送
    ElMessage.success('测试邮件发送成功')
  } catch (error) {
    ElMessage.error('测试邮件发送失败')
  }
}

const previewNotificationTemplate = () => {
  // TODO: 打开通知模板预览对话框
  ElMessage.info('通知模板预览功能开发中')
}





const resetSystemForm = () => {
  systemFormRef.value.resetFields()
}

const resetSecurityForm = () => {
  securityFormRef.value.resetFields()
}

const resetNotificationForm = () => {
  notificationFormRef.value.resetFields()
}





// 权限管理方法
const getRoleTagType = (level: number) => {
  if (level >= 9) return 'danger'
  if (level >= 7) return 'warning'
  if (level >= 5) return 'primary'
  return 'info'
}

const toggleRoleStatus = (role: any) => {
  ElMessage.success(`角色 ${role.name} 已${role.enabled ? '启用' : '禁用'}`)
}

const editRole = (role: any) => {
  ElMessage.info(`编辑角色: ${role.name}`)
  // TODO: 打开角色编辑对话框
}

const deleteRole = async (role: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除角色 "${role.name}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 调用API删除角色
    ElMessage.success('角色删除成功')
  } catch {
    // 用户取消
  }
}

const addNewRole = () => {
  ElMessage.info('添加新角色功能开发中')
  // TODO: 打开添加角色对话框
}

const loadRolePermissions = (roleId: number) => {
  // TODO: 根据角色ID加载权限
  const mockPermissions = roleId === 1 ?
    ['user_view', 'user_create', 'user_edit', 'user_delete', 'system_settings', 'log_management'] :
    ['user_view', 'content_view']
  permissionForm.checkedPermissions = mockPermissions
}

const handlePermissionCheck = (data: any, checked: any) => {
  // TODO: 处理权限选择变化
  console.log('Permission check:', data, checked)
}

const getMethodTagType = (method: string) => {
  const types: Record<string, string> = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger'
  }
  return types[method] || 'info'
}

const updateApiPermission = (api: any) => {
  ElMessage.success(`API ${api.path} 权限已${api.allowed ? '开启' : '关闭'}`)
}

const savePermissionSettings = async () => {
  try {
    permissionLoading.value = true
    // TODO: 调用API保存权限配置
    ElMessage.success('权限配置保存成功')
  } catch (error) {
    ElMessage.error('权限配置保存失败')
  } finally {
    permissionLoading.value = false
  }
}

const resetPermissionForm = () => {
  permissionForm.selectedRole = null
  permissionForm.checkedPermissions = []
}

const exportPermissionConfig = () => {
  // TODO: 导出权限配置
  ElMessage.success('权限配置导出成功')
}





// API管理方法
const configureService = (service: any) => {
  ElMessage.info(`配置服务: ${service.name}`)
  // TODO: 打开服务配置对话框
}

const testService = async (service: any) => {
  try {
    // TODO: 调用API测试第三方服务
    ElMessage.success(`服务 ${service.name} 连接测试成功`)
  } catch (error) {
    ElMessage.error(`服务 ${service.name} 连接测试失败`)
  }
}

const toggleService = (service: any) => {
  ElMessage.success(`服务 ${service.name} 已${service.enabled ? '启用' : '禁用'}`)
}

const addThirdPartyService = () => {
  ElMessage.info('添加第三方服务功能开发中')
  // TODO: 打开添加服务对话框
}

const editWebhook = (webhook: any) => {
  ElMessage.info(`编辑Webhook: ${webhook.name}`)
  // TODO: 打开Webhook编辑对话框
}

const testWebhook = async (webhook: any) => {
  try {
    // TODO: 调用API测试Webhook
    ElMessage.success(`Webhook ${webhook.name} 测试成功`)
  } catch (error) {
    ElMessage.error(`Webhook ${webhook.name} 测试失败`)
  }
}

const deleteWebhook = async (webhook: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除Webhook "${webhook.name}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // TODO: 调用API删除Webhook
    const index = apiForm.webhooks.findIndex(w => w.id === webhook.id)
    if (index > -1) {
      apiForm.webhooks.splice(index, 1)
    }
    ElMessage.success('Webhook删除成功')
  } catch {
    // 用户取消
  }
}

const addWebhook = () => {
  ElMessage.info('添加Webhook功能开发中')
  // TODO: 打开添加Webhook对话框
}

const saveApiSettings = async () => {
  try {
    apiLoading.value = true
    // TODO: 调用API保存接口配置
    ElMessage.success('接口配置保存成功')
  } catch (error) {
    ElMessage.error('接口配置保存失败')
  } finally {
    apiLoading.value = false
  }
}

const resetApiForm = () => {
  apiFormRef.value.resetFields()
}

const viewApiDocs = () => {
  // TODO: 打开API文档页面
  window.open('/docs', '_blank')
}

// 登录页面图片管理方法
const beforeDecorativeUpload = (file: File) => {
  const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'].includes(file.type)
  const isValidSize = file.size / 1024 / 1024 < 2

  if (!isValidType) {
    ElMessage.error('只支持 JPG、PNG、WebP 格式的图片!')
    return false
  }
  if (!isValidSize) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const beforeBackgroundUpload = (file: File) => {
  const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'].includes(file.type)
  const isValidSize = file.size / 1024 / 1024 < 5

  if (!isValidType) {
    ElMessage.error('只支持 JPG、PNG、WebP 格式的图片!')
    return false
  }
  if (!isValidSize) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

const handleDecorativeImageChange = (file: any) => {
  if (!file.raw) return

  const reader = new FileReader()
  reader.onload = (e) => {
    loginPageForm.decorativeImage = e.target?.result as string
  }
  reader.readAsDataURL(file.raw)
}

const handleBackgroundImageChange = (file: any) => {
  if (!file.raw) return

  const reader = new FileReader()
  reader.onload = (e) => {
    loginPageForm.backgroundImage = e.target?.result as string
  }
  reader.readAsDataURL(file.raw)
}

const removeDecorativeImage = () => {
  loginPageForm.decorativeImage = ''
  if (decorativeUploadRef.value) {
    decorativeUploadRef.value.clearFiles()
  }
}

const removeBackgroundImage = () => {
  loginPageForm.backgroundImage = ''
  if (backgroundUploadRef.value) {
    backgroundUploadRef.value.clearFiles()
  }
}

const saveLoginPageSettings = async () => {
  try {
    loginPageSaving.value = true

    // 保存到localStorage
    if (loginPageForm.decorativeImage) {
      localStorage.setItem('login-decorative-image', loginPageForm.decorativeImage)
    } else {
      localStorage.removeItem('login-decorative-image')
    }

    if (loginPageForm.backgroundImage) {
      localStorage.setItem('login-background-image', loginPageForm.backgroundImage)
    } else {
      localStorage.removeItem('login-background-image')
    }

    // 触发storage事件，通知登录页面更新
    window.dispatchEvent(new StorageEvent('storage', {
      key: 'login-decorative-image',
      newValue: loginPageForm.decorativeImage
    }))

    window.dispatchEvent(new StorageEvent('storage', {
      key: 'login-background-image',
      newValue: loginPageForm.backgroundImage
    }))

    ElMessage.success('登录页面设置保存成功!')
  } catch (error) {
    ElMessage.error('保存失败，请重试')
    console.error('保存登录页面设置失败:', error)
  } finally {
    loginPageSaving.value = false
  }
}

const resetLoginPageSettings = () => {
  ElMessageBox.confirm('确定要重置登录页面设置吗？这将清除所有自定义图片。', '确认重置', {
    type: 'warning'
  }).then(() => {
    loginPageForm.decorativeImage = ''
    loginPageForm.backgroundImage = ''
    localStorage.removeItem('login-decorative-image')
    localStorage.removeItem('login-background-image')

    // 清除上传组件
    if (decorativeUploadRef.value) {
      decorativeUploadRef.value.clearFiles()
    }
    if (backgroundUploadRef.value) {
      backgroundUploadRef.value.clearFiles()
    }

    ElMessage.success('登录页面设置已重置')
  }).catch(() => {
    // 用户取消
  })
}

const previewLoginPage = () => {
  // 在新窗口打开登录页面预览
  const loginUrl = window.location.origin + '/login'
  window.open(loginUrl, '_blank')
}

// 主题管理方法
const changeThemeMode = (mode: string) => {
  // TODO: 应用主题模式
  document.documentElement.setAttribute('data-theme', mode)
  ElMessage.success(`已切换到${mode === 'light' ? '浅色' : mode === 'dark' ? '深色' : '自动'}模式`)
}

const changePrimaryColor = (color: string) => {
  // TODO: 应用主色调
  document.documentElement.style.setProperty('--el-color-primary', color)
  ElMessage.success('主色调已更新')
}

const applyPresetTheme = (preset: any) => {
  themeForm.selectedPreset = preset.name
  themeForm.primaryColor = preset.primaryColor
  changePrimaryColor(preset.primaryColor)
  ElMessage.success(`已应用${preset.label}主题`)
}

const toggleCompactMode = (enabled: boolean) => {
  // TODO: 切换紧凑模式
  document.body.classList.toggle('compact-mode', enabled)
  ElMessage.success(`紧凑模式已${enabled ? '开启' : '关闭'}`)
}

const toggleFixedHeader = (enabled: boolean) => {
  // TODO: 切换固定头部
  document.body.classList.toggle('fixed-header', enabled)
  ElMessage.success(`固定头部已${enabled ? '开启' : '关闭'}`)
}



const toggleAnimations = (enabled: boolean) => {
  // TODO: 切换动画效果
  document.body.classList.toggle('no-animations', !enabled)
  ElMessage.success(`动画效果已${enabled ? '开启' : '关闭'}`)
}

const changeFontSize = (size: string) => {
  // TODO: 切换字体大小
  document.documentElement.setAttribute('data-font-size', size)
  ElMessage.success(`字体大小已切换为${size === 'small' ? '小' : size === 'medium' ? '中' : '大'}`)
}

const previewCustomCss = () => {
  // TODO: 预览自定义CSS
  ElMessage.info('CSS预览功能开发中')
}

const resetCustomCss = () => {
  themeForm.customCss = ''
  ElMessage.success('自定义CSS已重置')
}

const saveCustomTheme = () => {
  if (!themeForm.saveThemeName.trim()) {
    ElMessage.warning('请输入主题名称')
    return
  }

  const newTheme = {
    id: Date.now(),
    name: themeForm.saveThemeName,
    config: { ...themeForm }
  }

  themeForm.customThemes.push(newTheme)
  themeForm.saveThemeName = ''
  ElMessage.success('主题保存成功')
}

const handleThemeImportSuccess = (response: any) => {
  ElMessage.success('主题导入成功')
  // TODO: 应用导入的主题
}

const beforeThemeUpload = (file: File) => {
  const isJson = file.type === 'application/json'
  const isLt1M = file.size / 1024 / 1024 < 1

  if (!isJson) {
    ElMessage.error('只能上传JSON格式的主题文件!')
    return false
  }
  if (!isLt1M) {
    ElMessage.error('主题文件大小不能超过 1MB!')
    return false
  }
  return true
}

const exportCurrentTheme = () => {
  const themeConfig = {
    name: '导出主题',
    version: '1.0.0',
    config: { ...themeForm }
  }

  const blob = new Blob([JSON.stringify(themeConfig, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'theme-export.json'
  a.click()
  URL.revokeObjectURL(url)

  ElMessage.success('主题导出成功')
}

const applyCustomTheme = (theme: any) => {
  // TODO: 应用自定义主题
  ElMessage.success(`已应用主题: ${theme.name}`)
}

const editCustomTheme = (theme: any) => {
  ElMessage.info(`编辑主题: ${theme.name}`)
  // TODO: 打开主题编辑对话框
}

const deleteCustomTheme = async (theme: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除主题 "${theme.name}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const index = themeForm.customThemes.findIndex(t => t.id === theme.id)
    if (index > -1) {
      themeForm.customThemes.splice(index, 1)
    }
    ElMessage.success('主题删除成功')
  } catch {
    // 用户取消
  }
}

const saveThemeSettings = async () => {
  try {
    themeLoading.value = true
    // TODO: 调用API保存主题设置
    ElMessage.success('主题设置保存成功')
  } catch (error) {
    ElMessage.error('主题设置保存失败')
  } finally {
    themeLoading.value = false
  }
}

const resetThemeForm = () => {
  themeFormRef.value.resetFields()
}

const resetToDefault = async () => {
  try {
    await ElMessageBox.confirm('确定要恢复到默认主题设置吗？', '确认重置', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 重置主题设置
    Object.assign(themeForm, {
      themeMode: 'light',
      primaryColor: '#409eff',
      selectedPreset: 'default',
      sidebarWidth: 240,
      contentMaxWidth: '1200px',
      compactMode: false,
      fixedHeader: true,
      showBreadcrumb: true,
      showFooter: true,
      locale: 'zh-CN',
      dateFormat: 'YYYY-MM-DD',
      timeFormat: '24',
      numberFormat: 'zh',
      enableAnimations: true,
      enableSounds: false,
      fontSize: 'medium',
      borderRadius: 4,
      customCss: ''
    })

    // 应用默认设置
    changeThemeMode('light')
    changePrimaryColor('#409eff')

    ElMessage.success('已恢复默认主题设置')
  } catch {
    // 用户取消
  }
}

// AI配置管理方法
const getProviderName = (provider: AIProvider) => {
  const names = {
    qwen: '通义千问',
    deepseek: 'DeepSeek',
    openai: 'OpenAI',
    kimi: 'Kimi',
    doubao: '豆包',
    claude: 'Claude',
    gemini: 'Gemini',
    baichuan: '百川智能',
    chatglm: '智谱AI',
    wenxin: '文心一言',
    spark: '讯飞星火',
    minimax: 'MiniMax',
    custom: '自定义'
  }
  return names[provider] || provider
}

const getProviderTagType = (provider: AIProvider) => {
  const types = {
    qwen: 'primary',
    deepseek: 'success',
    openai: 'warning',
    kimi: 'info',
    doubao: 'danger',
    claude: 'primary',
    gemini: 'success',
    baichuan: 'warning',
    chatglm: 'info',
    wenxin: 'primary',
    spark: 'success',
    minimax: 'warning',
    custom: ''
  }
  return types[provider] || ''
}

const getProviderPresets = (provider: AIProvider) => {
  return AI_MODEL_PRESETS.filter(preset => preset.provider === provider)
}

const toggleAIFeature = (enabled: boolean) => {
  ElMessage.success(`AI功能已${enabled ? '启用' : '禁用'}`)
}

const addAIModel = () => {
  aiModelDialog.visible = true
  aiModelDialog.isEdit = false
  aiModelDialog.selectedPreset = ''
  resetAIModelForm()
}

const editAIModel = (model: AIModelConfig) => {
  aiModelDialog.visible = true
  aiModelDialog.isEdit = true
  Object.assign(aiModelDialog.form, { ...model })
}

const resetAIModelForm = () => {
  Object.assign(aiModelDialog.form, {
    id: '',
    name: '',
    provider: '',
    display_name: '',
    description: '',
    api_key: '',
    api_endpoint: '',
    model_name: '',
    parameters: {
      temperature: 0.7,
      max_tokens: 2000,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: false,
      timeout: 30,
      retry_count: 3
    },
    enabled: true,
    is_default: false
  })
}

const onProviderChange = (provider: AIProvider) => {
  aiModelDialog.selectedPreset = ''
  if (provider !== 'custom') {
    const presets = getProviderPresets(provider)
    if (presets.length > 0) {
      // 自动选择第一个预设
      aiModelDialog.selectedPreset = presets[0].name
      applyPreset(presets[0].name)
    }
  }
}

const applyPreset = (presetName: string) => {
  const preset = AI_MODEL_PRESETS.find(p => p.name === presetName)
  if (preset) {
    Object.assign(aiModelDialog.form, {
      display_name: preset.display_name,
      description: preset.description,
      api_endpoint: preset.api_endpoint,
      model_name: preset.model_name,
      parameters: { ...preset.default_parameters }
    })
  }
}

const testAIModel = async (model: AIModelConfig) => {
  try {
    model.testing = true
    // TODO: 调用API测试AI模型连接
    await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟测试
    ElMessage.success(`模型 ${model.display_name} 连接测试成功`)
  } catch (error) {
    ElMessage.error(`模型 ${model.display_name} 连接测试失败`)
  } finally {
    model.testing = false
  }
}

const testCurrentModel = async () => {
  try {
    aiModelDialog.testing = true
    // TODO: 调用API测试当前配置的模型
    await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟测试
    ElMessage.success('模型连接测试成功')
  } catch (error) {
    ElMessage.error('模型连接测试失败')
  } finally {
    aiModelDialog.testing = false
  }
}

const toggleAIModel = (model: AIModelConfig) => {
  ElMessage.success(`模型 ${model.display_name} 已${model.enabled ? '启用' : '禁用'}`)
}

const deleteAIModel = async (model: AIModelConfig) => {
  try {
    await ElMessageBox.confirm(`确定要删除模型 "${model.display_name}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const index = aiForm.models.findIndex(m => m.id === model.id)
    if (index > -1) {
      aiForm.models.splice(index, 1)
    }
    ElMessage.success('模型删除成功')
  } catch {
    // 用户取消
  }
}

const saveAIModel = async () => {
  try {
    await aiModelFormRef.value.validate()
    aiModelDialog.saving = true

    if (aiModelDialog.isEdit) {
      // 更新现有模型
      const index = aiForm.models.findIndex(m => m.id === aiModelDialog.form.id)
      if (index > -1) {
        Object.assign(aiForm.models[index], aiModelDialog.form)
      }
      ElMessage.success('模型更新成功')
    } else {
      // 添加新模型
      const newModel = {
        ...aiModelDialog.form,
        id: Date.now().toString(),
        name: aiModelDialog.form.model_name || '',
        created_at: new Date().toISOString()
      } as AIModelConfig

      aiForm.models.push(newModel)
      ElMessage.success('模型添加成功')
    }

    closeAIModelDialog()
  } catch (error) {
    ElMessage.error('请检查表单输入')
  } finally {
    aiModelDialog.saving = false
  }
}

const closeAIModelDialog = () => {
  aiModelDialog.visible = false
  resetAIModelForm()
}

const saveAISettings = async () => {
  try {
    aiLoading.value = true
    // TODO: 调用API保存AI配置
    ElMessage.success('AI配置保存成功')
  } catch (error) {
    ElMessage.error('AI配置保存失败')
  } finally {
    aiLoading.value = false
  }
}

const resetAIForm = () => {
  aiForm.aiEnabled = false
  aiForm.defaultModel = ''
  aiForm.models = []
}

const importAIConfig = () => {
  ElMessage.info('AI配置导入功能开发中')
  // TODO: 实现配置导入功能
}

const exportAIConfig = () => {
  const config = {
    ai_enabled: aiForm.aiEnabled,
    default_model: aiForm.defaultModel,
    models: aiForm.models
  }

  const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'ai-config.json'
  a.click()
  URL.revokeObjectURL(url)

  ElMessage.success('AI配置导出成功')
}

// 刷新数据统计
const refreshDataStats = () => {
  // TODO: 调用API刷新数据统计
  console.log('Refreshing data stats...')
}

// 刷新性能数据
const refreshPerformanceData = () => {
  // TODO: 调用API刷新性能数据
  console.log('Refreshing performance data...')
}

onMounted(() => {
  // TODO: 加载设置数据
  refreshDataStats()
  refreshPerformanceData()

  // 应用当前主题设置
  changeThemeMode(themeForm.themeMode)
  changePrimaryColor(themeForm.primaryColor)

  // 初始化AI配置示例数据
  aiForm.models = [
    {
      id: '1',
      name: 'qwen-turbo',
      provider: 'qwen' as AIProvider,
      display_name: '通义千问 Turbo',
      description: '阿里云通义千问快速模型',
      api_key: '***',
      api_endpoint: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
      model_name: 'qwen-turbo',
      parameters: {
        temperature: 0.7,
        max_tokens: 2000,
        top_p: 0.9,
        frequency_penalty: 0,
        presence_penalty: 0,
        stream: false,
        timeout: 30,
        retry_count: 3
      },
      enabled: true,
      is_default: true,
      created_at: new Date().toISOString()
    },
    {
      id: '2',
      name: 'deepseek-chat',
      provider: 'deepseek' as AIProvider,
      display_name: 'DeepSeek Chat',
      description: 'DeepSeek 对话模型，擅长代码生成',
      api_key: '***',
      api_endpoint: 'https://api.deepseek.com/v1/chat/completions',
      model_name: 'deepseek-chat',
      parameters: {
        temperature: 0.7,
        max_tokens: 4000,
        top_p: 0.95,
        frequency_penalty: 0,
        presence_penalty: 0,
        stream: false,
        timeout: 30,
        retry_count: 3
      },
      enabled: true,
      is_default: false,
      created_at: new Date().toISOString()
    },
    {
      id: '3',
      name: 'claude-3-haiku',
      provider: 'claude' as AIProvider,
      display_name: 'Claude 3 Haiku',
      description: 'Anthropic Claude 3 Haiku 模型，快速响应',
      api_key: '***',
      api_endpoint: 'https://api.anthropic.com/v1/messages',
      model_name: 'claude-3-haiku-20240307',
      parameters: {
        temperature: 0.7,
        max_tokens: 4000,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0,
        stream: false,
        timeout: 30,
        retry_count: 3
      },
      enabled: false,
      is_default: false,
      created_at: new Date().toISOString()
    }
  ]
  aiForm.aiEnabled = true
  aiForm.defaultModel = '1'
})
</script>

<style scoped>

.settings-page {
  @extend .page-container;

  .settings-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  /* 现代化标签页样式 */
  .modern-tabs {
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
    overflow: hidden;

    .el-tabs__header {
      background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
      margin: 0;
      border-bottom: 2px solid var(--border-light);

      .el-tabs__nav-wrap {
        padding: var(--spacing-4) var(--spacing-6);

        .el-tabs__nav {
          .el-tabs__item {
            border: none;
            background: transparent;
            color: var(--text-secondary);
            font-weight: var(--font-weight-medium);
            font-size: var(--font-size-base);
            padding: var(--spacing-3) var(--spacing-5);
            margin-right: var(--spacing-2);
            border-radius: var(--radius-lg);
            transition: var(--transition-normal);

            &:hover {
              color: var(--primary-color);
              background-color: rgba(79, 70, 229, 0.1);
              transform: translateY(-2px);
            }

            &.is-active {
              color: var(--primary-color);
              background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
              border: 1px solid rgba(79, 70, 229, 0.2);
              font-weight: var(--font-weight-semibold);
            }
          }
        }
      }
    }

    .el-tabs__content {
      padding: var(--spacing-8);
    }
  }

  /* 设置卡片样式 */
  .settings-card {
    @extend .card-modern;
    border: none;
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-6);

    .el-card__header {
      background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
      border-bottom: 1px solid var(--border-light);
      padding: var(--spacing-5) var(--spacing-6);

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        span {
          font-weight: var(--font-weight-semibold);
          color: var(--text-primary);
          font-size: var(--font-size-lg);
        }
      }
    }

    .el-card__body {
      padding: var(--spacing-6);
    }

    /* 表单样式 */
    .el-form {
      @extend .enhanced-form;

      .form-tip {
        display: block;
        margin-top: var(--spacing-2);
        font-size: var(--font-size-xs);
        color: var(--text-tertiary);
        line-height: var(--line-height-normal);
      }
    }
  }

  /* 上传组件样式 */
  .logo-uploader {
    border: 2px dashed var(--border-medium);
    border-radius: var(--radius-xl);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--transition-normal);
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-secondary);

    &:hover {
      border-color: var(--primary-color);
      background-color: var(--bg-accent);
      transform: scale(1.02);
    }

    .logo-uploader-icon {
      font-size: var(--font-size-3xl);
      color: var(--text-quaternary);
    }

    .logo {
      width: 120px;
      height: 120px;
      object-fit: cover;
      border-radius: var(--radius-lg);
    }
  }

  .favicon-uploader {
    border: 2px dashed var(--border-medium);
    border-radius: var(--radius-lg);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--transition-normal);
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-secondary);

    &:hover {
      border-color: var(--primary-color);
      background-color: var(--bg-accent);
      transform: scale(1.05);
    }

    .favicon-uploader-icon {
      font-size: var(--font-size-xl);
      color: var(--text-quaternary);
    }

    .favicon {
      width: 64px;
      height: 64px;
      object-fit: cover;
      border-radius: var(--radius-md);
    }
  }

  /* 表单元素样式 */
  .el-form-item {
    margin-bottom: var(--spacing-6);
  }

  .el-button + .el-button {
    margin-left: var(--spacing-3);
  }

  .el-divider {
    margin: var(--spacing-8) 0 var(--spacing-5) 0;
    border-color: var(--border-light);

    .el-divider__text {
      font-weight: var(--font-weight-semibold);
      color: var(--text-primary);
      font-size: var(--font-size-base);
      background-color: var(--bg-primary);
      padding: 0 var(--spacing-4);
    }
  }

  .el-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);

    .el-checkbox {
      margin-right: 0;
      padding: var(--spacing-2);
      border-radius: var(--radius-md);
      transition: var(--transition-fast);

      &:hover {
        background-color: var(--bg-accent);
      }

      .el-checkbox__label {
        font-weight: var(--font-weight-medium);
        color: var(--text-secondary);
      }

      &.is-checked .el-checkbox__label {
        color: var(--text-primary);
      }
    }
  }
}

.el-statistic {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.el-statistic .el-statistic__content {
  color: #409eff;
}

.el-row {
  margin-bottom: 0;
}

.permission-node {
  display: flex;
  align-items: center;
  gap: 8px;
}

.permission-node .el-icon {
  color: #409eff;
}

.permission-node .el-tag {
  margin-left: auto;
}

.el-tree {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.el-table {
  margin-bottom: 16px;
}

.el-table .el-button {
  margin-right: 8px;
}

.el-table .el-button:last-child {
  margin-right: 0;
}

.performance-stats .stat-card {
  text-align: center;
  padding: 16px;
}

.performance-stats .el-statistic {
  margin-bottom: 12px;
}

.performance-stats .el-progress {
  margin-top: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.card-header .el-button {
  margin-left: auto;
}

.stat-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: box-shadow 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.el-tag + .el-tag {
  margin-left: 5px;
}

.theme-presets {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.theme-preset-item {
  cursor: pointer;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.3s;
  text-align: center;
  min-width: 80px;
}

.theme-preset-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.theme-preset-item.active {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.theme-preview {
  display: flex;
  height: 20px;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.color-bar {
  flex: 1;
}

.theme-name {
  font-size: 12px;
  color: #606266;
}

.custom-themes {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
}

.custom-theme-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e4e7ed;
}

.custom-theme-item:last-child {
  border-bottom: none;
}

.custom-theme-item .theme-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.theme-actions {
  display: flex;
  gap: 8px;
}

.theme-actions .el-button {
  margin: 0;
}

/* 主题模式样式 */
[data-theme="dark"] {
  --el-bg-color: #1a1a1a;
  --el-text-color-primary: #e5eaf3;
  --el-text-color-regular: #cfd3dc;
  --el-border-color: #4c4d4f;
}

[data-font-size="small"] {
  font-size: 12px;
}

[data-font-size="large"] {
  font-size: 16px;
}

.compact-mode .el-form-item {
  margin-bottom: 16px;
}

.compact-mode .el-card {
  padding: 12px;
}

.no-animations * {
  transition: none !important;
  animation: none !important;
}

.model-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.model-name {
  font-weight: 500;
  color: #303133;
}

.el-dialog .el-form {
  max-height: 60vh;
  overflow-y: auto;
}

.el-dialog .el-form-item {
  margin-bottom: 18px;
}

.el-slider {
  margin: 8px 0;
}

@media (max-width: 768px) {
  .settings-page {
    padding: 10px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  .el-form-item {
    margin-bottom: 18px;
  }
}

/* 登录页面图片上传样式 */
.image-upload-section {
  .image-preview {
    position: relative;
    width: 200px;
    height: 150px;
    border: 2px dashed var(--border-light);
    border-radius: var(--radius-lg);
    overflow: hidden;
    margin-bottom: var(--spacing-4);

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .image-actions {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover .image-actions {
      opacity: 1;
    }

    &.background-preview {
      width: 300px;
      height: 180px;
    }
  }

  .upload-placeholder {
    width: 200px;
    height: 150px;
    border: 2px dashed var(--border-light);
    border-radius: var(--radius-lg);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-tertiary);
    margin-bottom: var(--spacing-4);

    p {
      margin: var(--spacing-2) 0 0;
      font-size: var(--font-size-sm);
    }

    &.background-placeholder {
      width: 300px;
      height: 180px;
    }
  }

  .upload-controls {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);

    .form-tip {
      font-size: var(--font-size-xs);
      color: var(--text-tertiary);
      margin-left: var(--spacing-2);
    }
  }
}
</style>
