"""
系统信息相关API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional
import psutil
import os
import time
import subprocess
import platform
from datetime import datetime, timedelta
import logging

from app.core.database import get_db
from app.core.security import get_current_user, get_current_active_superuser
from app.services.log_service import get_log_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/system", tags=["系统信息"])

def get_network_latency(host="*******", timeout=3):
    """
    获取网络延迟
    """
    try:
        system = platform.system().lower()

        if system == "windows":
            # Windows系统使用ping命令
            cmd = ["ping", "-n", "1", "-w", str(timeout * 1000), host]
        else:
            # Unix系统使用ping命令
            cmd = ["ping", "-c", "1", "-W", str(timeout), host]

        start_time = time.time()
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=timeout + 1
        )
        end_time = time.time()

        if result.returncode == 0:
            # 解析ping结果获取延迟时间
            output = result.stdout.lower()

            if system == "windows":
                # Windows ping输出格式: "时间=XXXms" 或 "time=XXXms"
                import re
                # 先尝试匹配中文格式
                match = re.search(r'时间=(\d+)ms', result.stdout)
                if match:
                    return int(match.group(1))
                # 再尝试匹配英文格式
                match = re.search(r'time[=<](\d+)ms', output)
                if match:
                    return int(match.group(1))
            else:
                # Unix ping输出格式: "time=XXX ms"
                import re
                match = re.search(r'time=(\d+\.?\d*)', output)
                if match:
                    latency = float(match.group(1))
                    return int(latency)

            # 如果无法解析，使用总时间作为估算
            total_time = (end_time - start_time) * 1000
            return int(total_time)
        else:
            return None

    except subprocess.TimeoutExpired:
        logger.warning(f"网络延迟检测超时: {host}")
        return None
    except Exception as e:
        logger.error(f"网络延迟检测失败: {e}")
        return None

@router.get("/info", summary="获取系统信息")
async def get_system_info(
    current_user: dict = Depends(get_current_user)
):
    """获取系统运行信息"""
    try:
        # 获取系统启动时间
        try:
            boot_time = psutil.boot_time()
            uptime_seconds = time.time() - boot_time
            uptime_formatted = str(timedelta(seconds=int(uptime_seconds)))
        except Exception as e:
            logger.error(f"获取系统启动时间失败: {e}")
            uptime_seconds = 0
            uptime_formatted = "未知"

        # 获取内存使用情况
        try:
            memory = psutil.virtual_memory()
            memory_info = {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "percent": memory.percent
            }
        except Exception as e:
            logger.error(f"获取内存信息失败: {e}")
            memory_info = {
                "total": 0,
                "available": 0,
                "used": 0,
                "percent": 0
            }

        # 获取CPU使用率
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
        except Exception as e:
            logger.error(f"获取CPU使用率失败: {e}")
            cpu_percent = 0

        # 获取磁盘使用情况 - Windows系统兼容性修复
        try:
            import platform
            if platform.system() == "Windows":
                # Windows系统使用C盘
                disk = psutil.disk_usage('C:')
            else:
                # Unix系统使用根目录
                disk = psutil.disk_usage('/')

            disk_info = {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "percent": round((disk.used / disk.total) * 100, 2) if disk.total > 0 else 0
            }
        except Exception as e:
            logger.error(f"获取磁盘使用情况失败: {e}")
            disk_info = {
                "total": 0,
                "used": 0,
                "free": 0,
                "percent": 0
            }

        # 获取进程信息
        try:
            process_count = len(psutil.pids())
        except Exception as e:
            logger.error(f"获取进程信息失败: {e}")
            process_count = 0

        # 获取网络连接数
        try:
            connections = len(psutil.net_connections())
        except Exception as e:
            logger.error(f"获取网络连接数失败: {e}")
            connections = 0

        # 获取网络延迟
        try:
            network_latency = get_network_latency()
            if network_latency is None:
                # 如果主要DNS服务器失败，尝试备用服务器
                network_latency = get_network_latency("1.1.1.1")
                if network_latency is None:
                    network_latency = get_network_latency("114.114.114.114")
        except Exception as e:
            logger.error(f"获取网络延迟失败: {e}")
            network_latency = None

        system_info = {
            "version": "1.0.0",
            "uptime": int(uptime_seconds),
            "uptime_formatted": uptime_formatted,
            "memory_usage": memory_info,
            "cpu_usage": cpu_percent,
            "disk_usage": disk_info,
            "process_count": process_count,
            "network_connections": connections,
            "network_latency": network_latency,  # 新增网络延迟字段
            "database_status": "connected",  # 简化处理
            "cache_status": "active",        # 简化处理
            "timestamp": datetime.utcnow().isoformat()
        }

        logger.info("系统信息获取成功")
        return system_info
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统信息失败"
        )



@router.get("/health", summary="系统健康检查")
async def health_check():
    """系统健康检查"""
    try:
        # 检查磁盘空间
        disk = psutil.disk_usage('/')
        disk_usage_percent = (disk.used / disk.total) * 100
        
        # 检查内存使用
        memory = psutil.virtual_memory()
        memory_usage_percent = memory.percent
        
        # 检查CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        health_status = {
            "status": "healthy",
            "checks": {
                "disk_space": {
                    "status": "ok" if disk_usage_percent < 90 else "warning",
                    "usage_percent": disk_usage_percent,
                    "message": "磁盘空间正常" if disk_usage_percent < 90 else "磁盘空间不足"
                },
                "memory": {
                    "status": "ok" if memory_usage_percent < 85 else "warning",
                    "usage_percent": memory_usage_percent,
                    "message": "内存使用正常" if memory_usage_percent < 85 else "内存使用率过高"
                },
                "cpu": {
                    "status": "ok" if cpu_percent < 80 else "warning",
                    "usage_percent": cpu_percent,
                    "message": "CPU使用正常" if cpu_percent < 80 else "CPU使用率过高"
                }
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # 如果有任何警告，整体状态设为警告
        if any(check["status"] == "warning" for check in health_status["checks"].values()):
            health_status["status"] = "warning"
        
        return health_status
    except Exception as e:
        logger.error(f"系统健康检查失败: {e}")
        return {
            "status": "error",
            "message": f"健康检查失败: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }

@router.post("/restart", summary="重启系统服务")
async def restart_system(
    current_user: dict = Depends(get_current_active_superuser)
):
    """重启系统服务（仅限超级管理员）"""
    try:
        logger.warning(f"用户 {current_user.get('username')} 请求重启系统")
        
        # 这里应该实现实际的重启逻辑
        # 由于安全考虑，这里只是返回一个消息
        return {
            "success": True,
            "message": "系统重启请求已接收，请手动重启服务"
        }
    except Exception as e:
        logger.error(f"系统重启失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="系统重启失败"
        )

@router.get("/processes", summary="获取进程信息")
async def get_processes(
    current_user: dict = Depends(get_current_active_superuser)
):
    """获取系统进程信息"""
    try:
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
            try:
                proc_info = proc.info
                if proc_info['name'] and 'python' in proc_info['name'].lower():
                    processes.append({
                        "pid": proc_info['pid'],
                        "name": proc_info['name'],
                        "cpu_percent": proc_info['cpu_percent'] or 0,
                        "memory_percent": proc_info['memory_percent'] or 0,
                        "status": proc_info['status']
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # 按CPU使用率排序
        processes.sort(key=lambda x: x['cpu_percent'], reverse=True)
        
        return {
            "processes": processes[:20],  # 只返回前20个进程
            "total_count": len(processes)
        }
    except Exception as e:
        logger.error(f"获取进程信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取进程信息失败"
        )

@router.get("/logs/stats", summary="获取日志统计")
async def get_log_stats(
    current_user: dict = Depends(get_current_user)
):
    """获取日志统计信息"""
    try:
        import glob
        from collections import defaultdict
        
        log_dir = "logs"
        if not os.path.exists(log_dir):
            return {"total_files": 0, "total_size": 0, "level_counts": {}}
        
        total_files = 0
        total_size = 0
        level_counts = defaultdict(int)
        
        # 统计日志文件
        for file_path in glob.glob(os.path.join(log_dir, "*.log*")):
            if os.path.isfile(file_path):
                total_files += 1
                total_size += os.path.getsize(file_path)
                
                # 简单统计日志级别（这里只是示例）
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            if 'ERROR' in line:
                                level_counts['ERROR'] += 1
                            elif 'WARNING' in line:
                                level_counts['WARNING'] += 1
                            elif 'INFO' in line:
                                level_counts['INFO'] += 1
                            elif 'DEBUG' in line:
                                level_counts['DEBUG'] += 1
                except:
                    continue
        
        return {
            "total_files": total_files,
            "total_size": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "level_counts": dict(level_counts)
        }
    except Exception as e:
        logger.error(f"获取日志统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取日志统计失败"
        )

@router.get("/performance", summary="获取性能指标")
async def get_performance_metrics(
    current_user: dict = Depends(get_current_user)
):
    """获取系统性能指标"""
    try:
        # 获取CPU信息
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        cpu_times = psutil.cpu_times()
        
        # 获取内存信息
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        # 获取磁盘IO
        disk_io = psutil.disk_io_counters()
        
        # 获取网络IO
        net_io = psutil.net_io_counters()
        
        performance_data = {
            "cpu": {
                "count": cpu_count,
                "frequency": {
                    "current": cpu_freq.current if cpu_freq else 0,
                    "min": cpu_freq.min if cpu_freq else 0,
                    "max": cpu_freq.max if cpu_freq else 0
                },
                "times": {
                    "user": cpu_times.user,
                    "system": cpu_times.system,
                    "idle": cpu_times.idle
                }
            },
            "memory": {
                "virtual": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "percent": memory.percent
                },
                "swap": {
                    "total": swap.total,
                    "used": swap.used,
                    "free": swap.free,
                    "percent": swap.percent
                }
            },
            "disk_io": {
                "read_count": disk_io.read_count if disk_io else 0,
                "write_count": disk_io.write_count if disk_io else 0,
                "read_bytes": disk_io.read_bytes if disk_io else 0,
                "write_bytes": disk_io.write_bytes if disk_io else 0
            },
            "network_io": {
                "bytes_sent": net_io.bytes_sent if net_io else 0,
                "bytes_recv": net_io.bytes_recv if net_io else 0,
                "packets_sent": net_io.packets_sent if net_io else 0,
                "packets_recv": net_io.packets_recv if net_io else 0
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return performance_data
    except Exception as e:
        logger.error(f"获取性能指标失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取性能指标失败"
        )

# ==================== 系统日志管理 ====================

@router.get("/logs", summary="获取系统日志列表")
async def get_system_logs(
    page: int = 1,
    page_size: int = 20,
    level: Optional[str] = None,
    module: Optional[str] = None,
    user_id: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    keyword: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取系统日志列表，支持分页、搜索和过滤"""
    try:
        log_service = get_log_service(db)

        # 处理日期参数
        start_dt = None
        end_dt = None

        if start_date:
            try:
                start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            except ValueError:
                pass

        if end_date:
            try:
                end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            except ValueError:
                pass

        # 获取日志数据
        result = log_service.get_system_logs(
            page=page,
            page_size=page_size,
            level=level,
            module=module,
            user_id=user_id,
            start_date=start_dt,
            end_date=end_dt,
            keyword=keyword
        )

        # 格式化返回数据
        return {
            "items": [
                {
                    "id": log.id,
                    "user_id": log.user_id,
                    "username": log.username,
                    "action": log.action,
                    "module": log.module,
                    "level": log.level,
                    "message": log.message,
                    "details": log.details,
                    "ip_address": log.ip_address,
                    "created_at": log.created_at.isoformat() if log.created_at else None,
                    "updated_at": log.updated_at.isoformat() if log.updated_at else None
                } for log in result["items"]
            ],
            "total": result["total"],
            "page": result["page"],
            "page_size": result["page_size"],
            "pages": result["pages"]
        }
    except Exception as e:
        logger.error(f"获取系统日志失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统日志失败"
        )

@router.get("/logs/{log_id}", summary="获取系统日志详情")
async def get_system_log(
    log_id: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取单个系统日志的详细信息"""
    try:
        log_service = get_log_service(db)
        log = log_service.get_system_log_by_id(log_id)

        if not log:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="日志记录不存在"
            )

        return {
            "id": log.id,
            "user_id": log.user_id,
            "username": log.username,
            "action": log.action,
            "module": log.module,
            "level": log.level,
            "message": log.message,
            "details": log.details,
            "ip_address": log.ip_address,
            "created_at": log.created_at.isoformat() if log.created_at else None,
            "updated_at": log.updated_at.isoformat() if log.updated_at else None
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取系统日志详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统日志详情失败"
        )

@router.post("/logs", summary="创建系统日志")
async def create_system_log(
    log_data: dict,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """创建新的系统日志记录"""
    try:
        from app.models.admin import SystemLog

        # 创建日志记录
        log = SystemLog(
            user_id=log_data.get("user_id", current_user.get("id")),
            username=log_data.get("username", current_user.get("username")),
            action=log_data["action"],
            module=log_data["module"],
            level=log_data.get("level", "INFO"),
            message=log_data["message"],
            details=log_data.get("details"),
            ip_address=log_data.get("ip_address")
        )

        db.add(log)
        db.commit()
        db.refresh(log)

        return {
            "id": log.id,
            "message": "日志记录创建成功",
            "created_at": log.created_at.isoformat() if log.created_at else None
        }
    except Exception as e:
        db.rollback()
        logger.error(f"创建系统日志失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建系统日志失败"
        )

@router.delete("/logs/{log_id}", summary="删除系统日志")
async def delete_system_log(
    log_id: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_superuser)
):
    """删除指定的系统日志记录"""
    try:
        from app.models.admin import SystemLog

        log = db.query(SystemLog).filter(SystemLog.id == log_id).first()
        if not log:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="日志记录不存在"
            )

        db.delete(log)
        db.commit()

        return {"message": "日志记录删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除系统日志失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除系统日志失败"
        )

@router.post("/logs/batch-delete", summary="批量删除系统日志")
async def batch_delete_system_logs(
    log_ids: list[str],
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_superuser)
):
    """批量删除系统日志记录"""
    try:
        from app.models.admin import SystemLog

        if not log_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请提供要删除的日志ID列表"
            )

        # 查找要删除的日志
        logs = db.query(SystemLog).filter(SystemLog.id.in_(log_ids)).all()
        deleted_count = len(logs)

        # 批量删除
        for log in logs:
            db.delete(log)

        db.commit()

        return {
            "message": f"成功删除 {deleted_count} 条日志记录",
            "deleted_count": deleted_count
        }
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"批量删除系统日志失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量删除系统日志失败"
        )

@router.post("/logs/cleanup", summary="清理过期日志")
async def cleanup_system_logs(
    days: int = 30,
    level: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_superuser)
):
    """清理指定天数之前的系统日志"""
    try:
        from app.models.admin import SystemLog

        cutoff_date = datetime.now() - timedelta(days=days)

        # 构建查询
        query = db.query(SystemLog).filter(SystemLog.created_at < cutoff_date)

        # 如果指定了级别，只清理该级别的日志
        if level:
            query = query.filter(SystemLog.level == level)

        # 获取要删除的记录数
        count = query.count()

        # 执行删除
        query.delete(synchronize_session=False)
        db.commit()

        return {
            "message": f"成功清理 {count} 条过期日志记录",
            "deleted_count": count,
            "cutoff_date": cutoff_date.isoformat()
        }
    except Exception as e:
        db.rollback()
        logger.error(f"清理系统日志失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="清理系统日志失败"
        )

@router.get("/logs/modules", summary="获取日志模块列表")
async def get_log_modules(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取系统中所有的日志模块列表"""
    try:
        logger.info("开始获取日志模块列表")

        # 首先尝试从数据库获取
        try:
            log_service = get_log_service(db)
            modules = log_service.get_log_modules()
            logger.info(f"从数据库获取到 {len(modules)} 个模块: {modules}")

            # 如果数据库中没有数据，返回默认模块列表
            if not modules:
                logger.info("数据库中没有日志模块数据，返回默认模块列表")
                modules = [
                    'SYSTEM', 'AUTH', 'USER', 'API', 'DATABASE',
                    'CACHE', 'EMAIL', 'FILE', 'SYNC', 'ADMIN'
                ]
        except Exception as db_error:
            logger.error(f"从数据库获取日志模块失败: {db_error}")
            # 数据库查询失败，返回默认模块列表
            modules = [
                'SYSTEM', 'AUTH', 'USER', 'API', 'DATABASE',
                'CACHE', 'EMAIL', 'FILE', 'SYNC', 'ADMIN'
            ]

        return {
            "modules": modules
        }
    except Exception as e:
        logger.error(f"获取日志模块列表失败: {e}")
        # 即使出错也返回默认模块列表，而不是抛出异常
        return {
            "modules": [
                'SYSTEM', 'AUTH', 'USER', 'API', 'DATABASE',
                'CACHE', 'EMAIL', 'FILE', 'SYNC', 'ADMIN'
            ]
        }

@router.get("/logs/statistics", summary="获取日志统计信息")
async def get_log_statistics(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取日志统计信息"""
    try:
        log_service = get_log_service(db)
        stats = log_service.get_log_statistics()

        return stats
    except Exception as e:
        logger.error(f"获取日志统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取日志统计失败"
        )

# ==================== 实时日志流 ====================

@router.get("/logs/realtime", summary="获取实时日志")
async def get_realtime_logs(
    level: Optional[str] = None,
    module: Optional[str] = None,
    limit: int = 50,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取最新的实时日志数据"""
    try:
        from app.models.admin import SystemLog
        from sqlalchemy import desc

        # 构建查询
        query = db.query(SystemLog)

        # 级别过滤
        if level and level != 'ALL':
            query = query.filter(SystemLog.level == level)

        # 模块过滤
        if module:
            query = query.filter(SystemLog.module == module)

        # 获取最新的日志
        logs = query.order_by(desc(SystemLog.created_at)).limit(limit).all()

        return {
            "logs": [
                {
                    "id": log.id,
                    "timestamp": log.created_at.isoformat() if log.created_at else None,
                    "level": log.level,
                    "module": log.module,
                    "message": log.message,
                    "username": log.username,
                    "action": log.action,
                    "ip_address": log.ip_address
                } for log in logs
            ],
            "count": len(logs),
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"获取实时日志失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取实时日志失败"
        )

# ==================== 日志配置管理 ====================

@router.get("/logs/config", summary="获取日志配置")
async def get_log_config(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取当前的日志配置"""
    try:
        from app.models.settings import SystemSettings

        settings = db.query(SystemSettings).first()
        if not settings:
            # 返回默认配置
            return {
                "log_level": "INFO",
                "log_file_path": "logs/wiscude-admin.log",
                "log_retention_days": 30,
                "log_max_size_mb": 100,
                "log_rotation_enabled": True,
                "log_format": "standard"
            }

        return {
            "log_level": settings.log_level,
            "log_file_path": settings.log_file_path,
            "log_retention_days": settings.log_retention_days,
            "log_max_size_mb": settings.log_max_size_mb,
            "log_rotation_enabled": True,  # 默认启用
            "log_format": "standard"  # 默认格式
        }
    except Exception as e:
        logger.error(f"获取日志配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取日志配置失败"
        )

@router.put("/logs/config", summary="更新日志配置")
async def update_log_config(
    config_data: dict,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_superuser)
):
    """更新日志配置"""
    try:
        from app.models.settings import SystemSettings

        settings = db.query(SystemSettings).first()
        if not settings:
            # 创建新的设置记录
            settings = SystemSettings()
            db.add(settings)

        # 更新日志相关配置
        if "log_level" in config_data:
            settings.log_level = config_data["log_level"]
        if "log_file_path" in config_data:
            settings.log_file_path = config_data["log_file_path"]
        if "log_retention_days" in config_data:
            settings.log_retention_days = config_data["log_retention_days"]
        if "log_max_size_mb" in config_data:
            settings.log_max_size_mb = config_data["log_max_size_mb"]

        db.commit()
        db.refresh(settings)

        # 记录配置更改日志
        from app.models.admin import SystemLog
        config_log = SystemLog(
            user_id=current_user.get("id"),
            username=current_user.get("username"),
            action="UPDATE_LOG_CONFIG",
            module="SYSTEM",
            level="INFO",
            message="日志配置已更新",
            details=str(config_data),
            ip_address=None  # 可以从请求中获取
        )
        db.add(config_log)
        db.commit()

        return {
            "message": "日志配置更新成功",
            "config": {
                "log_level": settings.log_level,
                "log_file_path": settings.log_file_path,
                "log_retention_days": settings.log_retention_days,
                "log_max_size_mb": settings.log_max_size_mb
            }
        }
    except Exception as e:
        db.rollback()
        logger.error(f"更新日志配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新日志配置失败"
        )
