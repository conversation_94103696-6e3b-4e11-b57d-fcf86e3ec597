import request from './request'
import type { 
  SystemSettings, 
  SettingsCategory, 
  SettingsTestRequest, 
  SettingsTestResult,
  SettingsImportRequest,
  SettingsExportResponse
} from '@/types/settings'

export const settingsApi = {
  /**
   * 获取系统设置
   */
  getSettings(): Promise<SystemSettings> {
    return request({
      url: '/settings/',
      method: 'get'
    })
  },

  /**
   * 更新系统设置
   * @param settings 设置数据
   */
  updateSettings(settings: Partial<SystemSettings>): Promise<SystemSettings> {
    return request({
      url: '/settings/',
      method: 'put',
      data: settings
    })
  },

  /**
   * 重置系统设置
   */
  resetSettings(): Promise<SystemSettings> {
    return request({
      url: '/settings/reset',
      method: 'post'
    })
  },

  /**
   * 导出系统设置
   */
  exportSettings(): Promise<SettingsExportResponse> {
    return request({
      url: '/settings/export',
      method: 'get'
    })
  },

  /**
   * 导入系统设置
   * @param importData 导入数据
   */
  importSettings(importData: SettingsImportRequest): Promise<SystemSettings> {
    return request({
      url: '/settings/import',
      method: 'post',
      data: importData
    })
  },

  /**
   * 从文件导入系统设置
   * @param file 配置文件
   * @param overwrite 是否覆盖敏感信息
   */
  importSettingsFromFile(file: File, overwrite: boolean = false): Promise<SystemSettings> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('overwrite', overwrite.toString())

    return request({
      url: '/settings/import-file',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 测试数据库连接
   * @param config 数据库配置
   */
  testDatabaseConnection(config: Record<string, any>): Promise<SettingsTestResult> {
    return request({
      url: '/settings/test-connection',
      method: 'post',
      data: {
        test_type: 'database',
        config
      }
    })
  },

  /**
   * 测试邮件配置
   * @param config 邮件配置
   */
  testEmailConfiguration(config: Record<string, any>): Promise<SettingsTestResult> {
    return request({
      url: '/settings/test-email',
      method: 'post',
      data: {
        test_type: 'email',
        config
      }
    })
  },

  /**
   * 获取设置分类
   */
  getSettingsCategories(): Promise<{ categories: SettingsCategory[] }> {
    return request({
      url: '/settings/categories',
      method: 'get'
    })
  },

  /**
   * 发送测试邮件
   */
  sendTestEmail(email: string): Promise<SettingsTestResult> {
    return request({
      url: '/settings/send-test-email',
      method: 'post',
      data: { email }
    })
  },

  /**
   * 获取日志文件列表
   */
  getLogFiles(): Promise<Array<{
    name: string
    size: number
    modified: string
    path: string
  }>> {
    return request({
      url: '/settings/logs/files',
      method: 'get'
    })
  },

  /**
   * 获取日志文件内容
   */
  getLogFileContent(filename: string): Promise<{ content: string }> {
    return request({
      url: `/settings/logs/content/${filename}`,
      method: 'get'
    })
  },

  /**
   * 下载日志文件
   */
  downloadLogFile(filename: string): Promise<Blob> {
    return request({
      url: `/settings/logs/download/${filename}`,
      method: 'get',
      responseType: 'blob'
    })
  },

  /**
   * 删除日志文件
   */
  deleteLogFile(filename: string): Promise<{ success: boolean; message: string }> {
    return request({
      url: `/settings/logs/file/${filename}`,
      method: 'delete'
    })
  },

  /**
   * 清理过期日志
   */
  cleanupOldLogs(): Promise<{ deleted: number; message: string }> {
    return request({
      url: '/settings/logs/cleanup',
      method: 'post'
    })
  },

  /**
   * 获取实时日志
   */
  getRealtimeLogs(level?: string, limit?: number): Promise<Array<{
    timestamp: string
    level: string
    message: string
    module?: string
  }>> {
    return request({
      url: '/settings/logs/realtime',
      method: 'get',
      params: { level, limit }
    })
  },

  /**
   * 验证设置项
   */
  validateSetting(key: string, value: any): Promise<{ valid: boolean; message?: string }> {
    return request({
      url: '/settings/validate',
      method: 'post',
      data: { key, value }
    })
  },

  /**
   * 获取设置项的默认值
   */
  getDefaultSettings(group?: string): Promise<Partial<SystemSettings>> {
    return request({
      url: '/settings/defaults',
      method: 'get',
      params: { group }
    })
  },

  /**
   * 应用设置更改
   */
  applySettings(settings: Partial<SystemSettings>): Promise<{
    success: boolean
    message: string
    requiresRestart: boolean
  }> {
    return request({
      url: '/settings/apply',
      method: 'post',
      data: settings
    })
  },

  /**
   * 获取系统信息
   */
  getSystemInfo(): Promise<{
    version: string
    uptime: number
    memory_usage: number
    cpu_usage: number
    disk_usage: number
    database_status: string
    cache_status: string
  }> {
    return request({
      url: '/system/info',
      method: 'get',
      skipGlobalErrorHandler: true
    })
  }
}
