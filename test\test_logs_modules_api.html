<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志模块API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #ecf0f1;
        }
        .success {
            color: #27ae60;
            background: #d5f4e6;
        }
        .error {
            color: #e74c3c;
            background: #fdf2f2;
        }
        .warning {
            color: #f39c12;
            background: #fef9e7;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .module-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        .module-tag {
            background: #3498db;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>日志模块API测试</h1>
        
        <div class="card">
            <h2>测试控制</h2>
            <button onclick="testWithoutAuth()">测试无认证调用</button>
            <button onclick="testWithAuth()">测试有认证调用</button>
            <button onclick="testDirectAPI()">直接API测试</button>
            <div class="status" id="overallStatus">点击按钮开始测试</div>
        </div>

        <div class="card">
            <h2>API响应结果</h2>
            <pre id="apiResponse">等待测试...</pre>
        </div>

        <div class="card">
            <h2>模块列表</h2>
            <div id="modulesList">等待获取模块列表...</div>
        </div>

        <div class="card">
            <h2>测试说明</h2>
            <ul>
                <li><strong>无认证调用</strong>：测试接口是否存在（应返回401认证错误）</li>
                <li><strong>有认证调用</strong>：使用localStorage中的token测试（应返回模块列表）</li>
                <li><strong>直接API测试</strong>：绕过前端框架直接调用API</li>
            </ul>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>无认证：HTTP 401 Unauthorized</li>
                <li>有认证：HTTP 200 OK + 模块列表</li>
                <li>不应该出现：HTTP 404 Not Found</li>
            </ul>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('overallStatus');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function updateResponse(data) {
            document.getElementById('apiResponse').textContent = JSON.stringify(data, null, 2);
        }

        function updateModulesList(modules) {
            const container = document.getElementById('modulesList');
            if (modules && modules.length > 0) {
                container.innerHTML = `
                    <p>获取到 ${modules.length} 个模块：</p>
                    <div class="module-list">
                        ${modules.map(module => `<span class="module-tag">${module}</span>`).join('')}
                    </div>
                `;
            } else {
                container.innerHTML = '<p>未获取到模块列表</p>';
            }
        }

        async function testWithoutAuth() {
            updateStatus('测试无认证调用...', 'warning');
            
            try {
                const response = await fetch('/api/system/logs/modules', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                const data = await response.json();
                
                updateResponse({
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    body: data
                });

                if (response.status === 401) {
                    updateStatus('✅ 无认证测试通过：接口存在，正确返回401认证错误', 'success');
                } else if (response.status === 404) {
                    updateStatus('❌ 接口不存在：返回404错误', 'error');
                } else {
                    updateStatus(`⚠️ 意外响应：${response.status} ${response.statusText}`, 'warning');
                }
            } catch (error) {
                updateStatus(`❌ 网络错误：${error.message}`, 'error');
                updateResponse({ error: error.message });
            }
        }

        async function testWithAuth() {
            updateStatus('测试有认证调用...', 'warning');
            
            const token = localStorage.getItem('access_token');
            if (!token) {
                updateStatus('❌ 未找到access_token，请先登录', 'error');
                updateResponse({ error: '未找到access_token' });
                return;
            }

            try {
                const response = await fetch('/api/system/logs/modules', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json'
                    }
                });

                const data = await response.json();
                
                updateResponse({
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    body: data
                });

                if (response.status === 200) {
                    updateStatus('✅ 有认证测试通过：成功获取模块列表', 'success');
                    updateModulesList(data.modules);
                } else if (response.status === 401) {
                    updateStatus('⚠️ Token可能已过期：返回401认证错误', 'warning');
                } else {
                    updateStatus(`❌ 意外响应：${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                updateStatus(`❌ 网络错误：${error.message}`, 'error');
                updateResponse({ error: error.message });
            }
        }

        async function testDirectAPI() {
            updateStatus('直接API测试...', 'warning');
            
            try {
                // 首先测试无认证
                console.log('测试无认证调用...');
                const noAuthResponse = await fetch('/api/system/logs/modules');
                const noAuthData = await noAuthResponse.json();
                
                console.log('无认证响应:', noAuthResponse.status, noAuthData);
                
                // 然后测试有认证（如果有token）
                const token = localStorage.getItem('access_token');
                let authData = null;
                let authResponse = null;
                
                if (token) {
                    console.log('测试有认证调用...');
                    authResponse = await fetch('/api/system/logs/modules', {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Accept': 'application/json'
                        }
                    });
                    authData = await authResponse.json();
                    console.log('有认证响应:', authResponse.status, authData);
                }

                const result = {
                    noAuth: {
                        status: noAuthResponse.status,
                        statusText: noAuthResponse.statusText,
                        data: noAuthData
                    },
                    withAuth: token ? {
                        status: authResponse.status,
                        statusText: authResponse.statusText,
                        data: authData
                    } : '未找到token'
                };

                updateResponse(result);

                // 分析结果
                if (noAuthResponse.status === 401) {
                    if (authResponse && authResponse.status === 200) {
                        updateStatus('✅ 完美：无认证返回401，有认证返回200', 'success');
                        updateModulesList(authData.modules);
                    } else if (authResponse && authResponse.status === 401) {
                        updateStatus('⚠️ Token可能过期：两次调用都返回401', 'warning');
                    } else {
                        updateStatus('✅ 接口正常：无认证正确返回401', 'success');
                    }
                } else if (noAuthResponse.status === 404) {
                    updateStatus('❌ 接口不存在：返回404错误', 'error');
                } else {
                    updateStatus(`⚠️ 意外行为：无认证返回${noAuthResponse.status}`, 'warning');
                }

            } catch (error) {
                updateStatus(`❌ 测试失败：${error.message}`, 'error');
                updateResponse({ error: error.message });
            }
        }

        // 页面加载时自动运行基本测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                testWithoutAuth();
            }, 1000);
        });
    </script>
</body>
</html>
