<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token状态检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #ecf0f1;
        }
        .success {
            color: #27ae60;
            background: #d5f4e6;
        }
        .error {
            color: #e74c3c;
            background: #fdf2f2;
        }
        .warning {
            color: #f39c12;
            background: #fef9e7;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .token-display {
            word-break: break-all;
            font-family: monospace;
            font-size: 11px;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Token状态检查</h1>
        
        <div class="card">
            <h2>控制面板</h2>
            <button onclick="checkTokens()">检查Token状态</button>
            <button onclick="testAPI()">测试API调用</button>
            <button onclick="clearTokens()">清除Token</button>
            <div class="status" id="overallStatus">点击按钮开始检查</div>
        </div>

        <div class="card">
            <h2>Token信息</h2>
            <div>
                <h3>Access Token</h3>
                <div id="accessTokenStatus" class="status">未检查</div>
                <div id="accessTokenDisplay" class="token-display">等待检查...</div>
            </div>
            <div>
                <h3>Refresh Token</h3>
                <div id="refreshTokenStatus" class="status">未检查</div>
                <div id="refreshTokenDisplay" class="token-display">等待检查...</div>
            </div>
        </div>

        <div class="card">
            <h2>API测试结果</h2>
            <pre id="apiResults">等待测试...</pre>
        </div>

        <div class="card">
            <h2>解决方案</h2>
            <div id="solutions">
                <p><strong>如果Token无效或不存在：</strong></p>
                <ol>
                    <li>请先登录管理员账户</li>
                    <li>确认登录成功后再访问日志管理页面</li>
                    <li>如果问题持续，请清除浏览器缓存</li>
                </ol>
                <p><strong>如果API调用失败：</strong></p>
                <ol>
                    <li>检查后端服务是否正常运行</li>
                    <li>确认网络连接正常</li>
                    <li>检查浏览器控制台是否有其他错误</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function checkTokens() {
            updateStatus('overallStatus', '正在检查Token状态...', 'warning');

            // 检查Access Token
            const accessToken = localStorage.getItem('access_token');
            if (accessToken) {
                updateStatus('accessTokenStatus', '✅ Access Token存在', 'success');
                document.getElementById('accessTokenDisplay').textContent = 
                    `${accessToken.substring(0, 50)}...${accessToken.substring(accessToken.length - 20)}`;
                
                // 尝试解析JWT
                try {
                    const payload = JSON.parse(atob(accessToken.split('.')[1]));
                    const exp = new Date(payload.exp * 1000);
                    const now = new Date();
                    
                    if (exp > now) {
                        updateStatus('accessTokenStatus', `✅ Access Token有效，过期时间: ${exp.toLocaleString()}`, 'success');
                    } else {
                        updateStatus('accessTokenStatus', `❌ Access Token已过期，过期时间: ${exp.toLocaleString()}`, 'error');
                    }
                } catch (e) {
                    updateStatus('accessTokenStatus', '⚠️ Access Token格式无效', 'warning');
                }
            } else {
                updateStatus('accessTokenStatus', '❌ Access Token不存在', 'error');
                document.getElementById('accessTokenDisplay').textContent = '未找到Access Token';
            }

            // 检查Refresh Token
            const refreshToken = localStorage.getItem('refresh_token');
            if (refreshToken) {
                updateStatus('refreshTokenStatus', '✅ Refresh Token存在', 'success');
                document.getElementById('refreshTokenDisplay').textContent = 
                    `${refreshToken.substring(0, 50)}...${refreshToken.substring(refreshToken.length - 20)}`;
            } else {
                updateStatus('refreshTokenStatus', '❌ Refresh Token不存在', 'error');
                document.getElementById('refreshTokenDisplay').textContent = '未找到Refresh Token';
            }

            updateStatus('overallStatus', 'Token状态检查完成', 'success');
        }

        async function testAPI() {
            updateStatus('overallStatus', '正在测试API调用...', 'warning');
            
            const results = [];
            const accessToken = localStorage.getItem('access_token');

            if (!accessToken) {
                results.push('❌ 无法测试API：没有Access Token');
                document.getElementById('apiResults').textContent = results.join('\n');
                updateStatus('overallStatus', 'API测试失败：没有Token', 'error');
                return;
            }

            // 测试认证接口
            try {
                results.push('🔍 测试 /api/auth/me...');
                const meResponse = await fetch('/api/auth/me', {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Accept': 'application/json'
                    }
                });

                if (meResponse.ok) {
                    const meData = await meResponse.json();
                    results.push(`✅ /api/auth/me 成功: ${meData.username} (${meData.role})`);
                } else {
                    results.push(`❌ /api/auth/me 失败: ${meResponse.status} ${meResponse.statusText}`);
                }
            } catch (error) {
                results.push(`❌ /api/auth/me 错误: ${error.message}`);
            }

            // 测试日志模块接口
            try {
                results.push('\n🔍 测试 /api/system/logs/modules...');
                const modulesResponse = await fetch('/api/system/logs/modules', {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Accept': 'application/json'
                    }
                });

                if (modulesResponse.ok) {
                    const modulesData = await modulesResponse.json();
                    results.push(`✅ /api/system/logs/modules 成功: 获取到 ${modulesData.modules?.length || 0} 个模块`);
                    if (modulesData.modules) {
                        results.push(`   模块列表: ${modulesData.modules.join(', ')}`);
                    }
                } else {
                    results.push(`❌ /api/system/logs/modules 失败: ${modulesResponse.status} ${modulesResponse.statusText}`);
                }
            } catch (error) {
                results.push(`❌ /api/system/logs/modules 错误: ${error.message}`);
            }

            // 测试日志列表接口
            try {
                results.push('\n🔍 测试 /api/system/logs...');
                const logsResponse = await fetch('/api/system/logs?page=1&page_size=5', {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Accept': 'application/json'
                    }
                });

                if (logsResponse.ok) {
                    const logsData = await logsResponse.json();
                    results.push(`✅ /api/system/logs 成功: 获取到 ${logsData.items?.length || 0} 条日志`);
                    results.push(`   总计: ${logsData.total || 0} 条日志`);
                } else {
                    results.push(`❌ /api/system/logs 失败: ${logsResponse.status} ${logsResponse.statusText}`);
                }
            } catch (error) {
                results.push(`❌ /api/system/logs 错误: ${error.message}`);
            }

            document.getElementById('apiResults').textContent = results.join('\n');
            updateStatus('overallStatus', 'API测试完成', 'success');
        }

        function clearTokens() {
            if (confirm('确定要清除所有Token吗？这将需要重新登录。')) {
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                localStorage.removeItem('token');
                updateStatus('overallStatus', 'Token已清除，请重新登录', 'warning');
                checkTokens();
            }
        }

        // 页面加载时自动检查
        window.addEventListener('load', () => {
            checkTokens();
        });
    </script>
</body>
</html>
