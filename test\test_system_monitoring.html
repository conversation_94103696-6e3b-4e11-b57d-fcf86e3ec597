<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监控测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
        .metric-label {
            color: #666;
            font-size: 0.9em;
        }
        .loading {
            color: #999;
        }
        .error {
            color: #e74c3c;
        }
        .success {
            color: #27ae60;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #ecf0f1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>系统监控测试页面</h1>
        
        <div class="card">
            <h2>控制面板</h2>
            <button onclick="loadSystemInfo()">手动刷新</button>
            <button onclick="toggleAutoRefresh()">切换自动刷新</button>
            <div class="status" id="status">准备就绪</div>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-label">CPU使用率</div>
                <div class="metric-value" id="cpu">加载中...</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">内存使用</div>
                <div class="metric-value" id="memory">加载中...</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">磁盘使用</div>
                <div class="metric-value" id="disk">加载中...</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">网络延迟</div>
                <div class="metric-value" id="network">加载中...</div>
            </div>
        </div>

        <div class="card">
            <h2>详细信息</h2>
            <pre id="details">等待数据...</pre>
        </div>
    </div>

    <script>
        let autoRefreshTimer = null;
        let autoRefreshEnabled = false;

        async function loadSystemInfo() {
            const status = document.getElementById('status');
            status.textContent = '正在加载...';
            status.className = 'status loading';

            try {
                const response = await fetch('/api/system/info', {
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                // 更新四个核心指标
                document.getElementById('cpu').textContent = `${data.cpu_usage.toFixed(1)}%`;
                document.getElementById('memory').textContent = `${data.memory_usage.percent.toFixed(1)}%`;
                document.getElementById('disk').textContent = `${data.disk_usage.percent.toFixed(1)}%`;
                document.getElementById('network').textContent = data.network_latency !== null ? `${data.network_latency}ms` : '超时';

                // 显示详细信息
                document.getElementById('details').textContent = JSON.stringify(data, null, 2);

                status.textContent = `数据更新成功 - ${new Date().toLocaleTimeString()}`;
                status.className = 'status success';

            } catch (error) {
                console.error('加载系统信息失败:', error);
                
                // 更新错误状态
                ['cpu', 'memory', 'disk', 'network'].forEach(id => {
                    document.getElementById(id).textContent = '加载失败';
                });

                document.getElementById('details').textContent = `错误: ${error.message}`;
                status.textContent = `加载失败: ${error.message}`;
                status.className = 'status error';
            }
        }

        function toggleAutoRefresh() {
            autoRefreshEnabled = !autoRefreshEnabled;
            
            if (autoRefreshEnabled) {
                autoRefreshTimer = setInterval(loadSystemInfo, 30000);
                document.getElementById('status').textContent = '自动刷新已启用 (30秒间隔)';
            } else {
                if (autoRefreshTimer) {
                    clearInterval(autoRefreshTimer);
                    autoRefreshTimer = null;
                }
                document.getElementById('status').textContent = '自动刷新已停用';
            }
        }

        // 页面加载时初始化
        window.addEventListener('load', () => {
            loadSystemInfo();
        });
    </script>
</body>
</html>
