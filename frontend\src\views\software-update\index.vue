<template>
  <div class="software-update-overview">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1>系统更新中心</h1>
          <p>管理系统版本，跟踪更新进度，维护系统稳定性</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="checkUpdates" :loading="checkingUpdates">
            <el-icon><Refresh /></el-icon>
            检查更新
          </el-button>
          <el-button @click="viewUpdateHistory">
            <el-icon><Clock /></el-icon>
            更新历史
          </el-button>
          <el-button @click="viewSystemInfo">
            <el-icon><Monitor /></el-icon>
            系统信息
          </el-button>
        </div>
      </div>
    </div>

    <!-- 当前版本信息 -->
    <div class="current-version">
      <el-card class="version-card">
        <div class="version-content">
          <div class="version-info">
            <div class="version-main">
              <h2>智慧教育管理系统</h2>
              <div class="version-number">
                <span class="version-label">当前版本：</span>
                <span class="version-value">v{{ currentVersion.version }}</span>
                <el-tag :type="getVersionStatusType(currentVersion.status)" size="small">
                  {{ getVersionStatusText(currentVersion.status) }}
                </el-tag>
              </div>
              <div class="version-meta">
                <span class="build-info">构建号：{{ currentVersion.buildNumber }}</span>
                <span class="release-date">发布时间：{{ formatDate(currentVersion.releaseDate) }}</span>
              </div>
            </div>
            <div class="version-actions">
              <el-button v-if="hasUpdates" type="primary" @click="startUpdate">
                <el-icon><Download /></el-icon>
                立即更新
              </el-button>
              <el-button @click="viewReleaseNotes">
                <el-icon><Document /></el-icon>
                版本说明
              </el-button>
            </div>
          </div>
          <div class="version-status">
            <div class="status-indicator" :class="getSystemStatusClass()">
              <el-icon><CircleCheck /></el-icon>
              <span>{{ getSystemStatusText() }}</span>
            </div>
            <div class="last-check">
              上次检查：{{ formatTime(lastCheckTime) }}
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 系统统计 -->
    <div class="system-stats">
      <el-row :gutter="24">
        <el-col :xs="24" :lg="8">
          <el-card class="stat-card">
            <template #header>
              <span>系统使用情况</span>
            </template>
            <div class="usage-stats">
              <div class="usage-item">
                <div class="usage-label">活跃用户</div>
                <div class="usage-value">{{ systemStats.activeUsers }}</div>
                <div class="usage-trend positive">
                  <el-icon><ArrowUp /></el-icon>
                  +{{ systemStats.userGrowth }}%
                </div>
              </div>
              <div class="usage-item">
                <div class="usage-label">系统负载</div>
                <div class="usage-value">{{ systemStats.systemLoad }}%</div>
                <div class="usage-trend" :class="getLoadTrendClass(systemStats.systemLoad)">
                  <el-icon><TrendCharts /></el-icon>
                  {{ getLoadStatus(systemStats.systemLoad) }}
                </div>
              </div>
              <div class="usage-item">
                <div class="usage-label">存储使用</div>
                <div class="usage-value">{{ systemStats.storageUsed }}GB</div>
                <div class="usage-progress">
                  <el-progress
                    :percentage="systemStats.storagePercent"
                    :stroke-width="6"
                    :show-text="false"
                  />
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 更新统计 -->
        <el-col :xs="24" :lg="8">
          <el-card class="stat-card">
            <template #header>
              <span>更新统计</span>
            </template>
            <div class="update-stats">
              <div class="stat-item">
                <div class="stat-icon success">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ updateStats.successfulUpdates }}</div>
                  <div class="stat-label">成功更新</div>
                </div>
              </div>
              <div class="stat-item">
                <div class="stat-icon warning">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ updateStats.pendingUpdates }}</div>
                  <div class="stat-label">待更新</div>
                </div>
              </div>
              <div class="stat-item">
                <div class="stat-icon error">
                  <el-icon><CircleClose /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ updateStats.failedUpdates }}</div>
                  <div class="stat-label">更新失败</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>


      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Refresh, Clock, Monitor, Download, Document, CircleCheck, ArrowUp,
  TrendCharts, Warning, CircleClose, Bell
} from '@element-plus/icons-vue'

// 路由
const router = useRouter()

// 响应式数据
const checkingUpdates = ref(false)
const hasUpdates = ref(false)
const lastCheckTime = ref('2024-01-20 10:30:00')

// 当前版本信息
const currentVersion = reactive({
  version: '2.1.3',
  buildNumber: '20240120.1',
  releaseDate: '2024-01-15',
  status: 'stable'
})

// 系统统计数据
const systemStats = reactive({
  activeUsers: 2847,
  systemLoad: 45,
  storageUsed: 156.8,
  storagePercent: 62,
  userGrowth: 12.5
})

// 更新统计数据
const updateStats = reactive({
  successfulUpdates: 23,
  pendingUpdates: 2,
  failedUpdates: 1
})



// 方法
const checkUpdates = async () => {
  checkingUpdates.value = true
  try {
    // 模拟检查更新
    await new Promise(resolve => setTimeout(resolve, 2000))
    lastCheckTime.value = new Date().toLocaleString()
    ElMessage.success('检查完成，系统已是最新版本')
  } catch (error) {
    ElMessage.error('检查更新失败')
  } finally {
    checkingUpdates.value = false
  }
}

const viewUpdateHistory = () => {
  router.push('/software-update/history')
}

const viewSystemInfo = () => {
  router.push('/software-update/system-info')
}

const startUpdate = () => {
  ElMessage.info('开始更新系统...')
}

const viewReleaseNotes = () => {
  router.push('/software-update/release-notes')
}

const getVersionStatusType = (status: string): string => {
  const types = {
    stable: 'success',
    beta: 'warning',
    alpha: 'danger'
  }
  return types[status as keyof typeof types] || 'default'
}

const getVersionStatusText = (status: string): string => {
  const texts = {
    stable: '稳定版',
    beta: '测试版',
    alpha: '开发版'
  }
  return texts[status as keyof typeof texts] || status
}

const getSystemStatusClass = (): string => {
  return 'status-good'
}

const getSystemStatusText = (): string => {
  return '系统运行正常'
}

const formatDate = (date: string): string => {
  return new Date(date).toLocaleDateString()
}

const formatTime = (time: string): string => {
  return new Date(time).toLocaleString()
}

const getLoadTrendClass = (load: number): string => {
  if (load < 50) return 'positive'
  if (load < 80) return 'warning'
  return 'negative'
}

const getLoadStatus = (load: number): string => {
  if (load < 50) return '正常'
  if (load < 80) return '中等'
  return '较高'
}



// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
@import '@/styles/design-system.scss';

.software-update-overview {
  padding: var(--spacing-6);
  background-color: var(--bg-secondary);
  min-height: 100vh;

  /* 页面头部 */
  .page-header {
    margin-bottom: var(--spacing-6);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: var(--spacing-4);

      .header-info {
        h1 {
          font-size: var(--font-size-3xl);
          font-weight: var(--font-weight-bold);
          color: var(--text-primary);
          margin: 0 0 var(--spacing-2) 0;
        }

        p {
          font-size: var(--font-size-base);
          color: var(--text-secondary);
          margin: 0;
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-3);
        flex-shrink: 0;

        .el-button {
          border-radius: var(--radius-lg);
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }

  /* 当前版本信息 */
  .current-version {
    margin-bottom: var(--spacing-8);

    .version-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);

      .version-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-6);

        .version-info {
          display: flex;
          align-items: center;
          gap: var(--spacing-6);

          .version-main {
            h2 {
              font-size: var(--font-size-xl);
              font-weight: var(--font-weight-bold);
              color: var(--text-primary);
              margin: 0 0 var(--spacing-2) 0;
            }

            .version-number {
              display: flex;
              align-items: center;
              gap: var(--spacing-2);
              margin-bottom: var(--spacing-2);

              .version-label {
                font-size: var(--font-size-base);
                color: var(--text-secondary);
              }

              .version-value {
                font-size: var(--font-size-lg);
                font-weight: var(--font-weight-bold);
                color: var(--text-primary);
              }
            }

            .version-meta {
              display: flex;
              gap: var(--spacing-4);
              font-size: var(--font-size-sm);
              color: var(--text-tertiary);
            }
          }

          .version-actions {
            display: flex;
            gap: var(--spacing-3);

            .el-button {
              border-radius: var(--radius-lg);
            }
          }
        }

        .version-status {
          text-align: right;

          .status-indicator {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
            margin-bottom: var(--spacing-2);
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-medium);

            &.status-good {
              color: var(--success-color);
            }
          }

          .last-check {
            font-size: var(--font-size-sm);
            color: var(--text-tertiary);
          }
        }
      }
    }
  }

  /* 系统统计 */
  .system-stats {
    margin-bottom: var(--spacing-8);

    .stat-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);

      .el-card__header {
        border-bottom: 1px solid var(--border-light);
        padding: var(--spacing-5) var(--spacing-6);

        span {
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-semibold);
          color: var(--text-primary);
        }
      }
    }

    /* 使用情况统计 */
    .usage-stats {
      padding: var(--spacing-4);

      .usage-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-3) 0;
        border-bottom: 1px solid var(--border-light);

        &:last-child {
          border-bottom: none;
        }

        .usage-label {
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
        }

        .usage-value {
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-bold);
          color: var(--text-primary);
        }

        .usage-trend {
          display: flex;
          align-items: center;
          gap: var(--spacing-1);
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-medium);

          &.positive {
            color: var(--success-color);
          }

          &.warning {
            color: var(--warning-color);
          }

          &.negative {
            color: var(--error-color);
          }
        }

        .usage-progress {
          width: 100px;
        }
      }
    }

    /* 更新统计 */
    .update-stats {
      padding: var(--spacing-4);

      .stat-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-3);
        padding: var(--spacing-3) 0;
        border-bottom: 1px solid var(--border-light);

        &:last-child {
          border-bottom: none;
        }

        .stat-icon {
          width: 40px;
          height: 40px;
          border-radius: var(--radius-lg);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-base);

          &.success {
            background: linear-gradient(135deg, var(--success-color), var(--success-light));
          }

          &.warning {
            background: linear-gradient(135deg, var(--warning-color), var(--warning-light));
          }

          &.error {
            background: linear-gradient(135deg, var(--error-color), var(--error-light));
          }
        }

        .stat-info {
          .stat-value {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .stat-label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
          }
        }
      }
    }


  }
}
</style>