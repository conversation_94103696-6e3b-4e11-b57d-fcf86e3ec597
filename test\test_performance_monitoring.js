// 测试性能监控页面的CPU图表切换功能
// 在浏览器控制台中运行此脚本

console.log('🔍 开始测试CPU图表切换功能...');

// 测试函数
function testCpuChartToggle() {
  // 查找切换按钮
  const toggleButton = document.querySelector('button[onclick*="toggleCpuChart"], button:contains("详细视图"), button:contains("简化视图")');
  
  if (!toggleButton) {
    console.error('❌ 未找到CPU图表切换按钮');
    return false;
  }
  
  console.log('✅ 找到切换按钮:', toggleButton.textContent);
  
  // 记录初始状态
  const initialText = toggleButton.textContent.trim();
  console.log('📊 初始按钮文本:', initialText);
  
  // 模拟点击
  try {
    toggleButton.click();
    console.log('🖱️ 已点击切换按钮');
    
    // 等待一段时间后检查状态变化
    setTimeout(() => {
      const newText = toggleButton.textContent.trim();
      console.log('📊 切换后按钮文本:', newText);
      
      if (newText !== initialText) {
        console.log('✅ 按钮文本已改变，切换功能正常');
        
        // 检查图表是否有变化
        const chartContainer = document.querySelector('.chart-container');
        if (chartContainer) {
          console.log('✅ 找到图表容器');
          
          // 检查是否有图例显示变化
          const legend = chartContainer.querySelector('.echarts-legend, .legend');
          if (legend) {
            console.log('✅ 图表图例存在，详细视图可能已激活');
          } else {
            console.log('ℹ️ 未找到图例，可能是简化视图');
          }
        }
        
        return true;
      } else {
        console.error('❌ 按钮文本未改变，切换功能可能有问题');
        return false;
      }
    }, 2000);
    
  } catch (error) {
    console.error('❌ 点击按钮时出错:', error);
    return false;
  }
}

// 检查页面是否加载完成
function checkPageReady() {
  const performanceCards = document.querySelectorAll('.metric-card, .performance-card');
  const chartContainers = document.querySelectorAll('.chart-container');
  
  console.log(`📈 找到 ${performanceCards.length} 个性能卡片`);
  console.log(`📊 找到 ${chartContainers.length} 个图表容器`);
  
  if (performanceCards.length > 0 && chartContainers.length > 0) {
    console.log('✅ 页面已加载完成');
    return true;
  } else {
    console.log('⏳ 页面仍在加载中...');
    return false;
  }
}

// 主测试流程
function runTest() {
  console.log('🚀 开始性能监控页面测试');
  
  if (!checkPageReady()) {
    console.log('⏳ 等待页面加载...');
    setTimeout(runTest, 2000);
    return;
  }
  
  // 测试CPU图表切换
  console.log('🔄 测试CPU图表切换功能...');
  testCpuChartToggle();
  
  // 检查其他功能
  console.log('🔍 检查其他页面元素...');
  
  const refreshButtons = document.querySelectorAll('button:contains("刷新"), button[onclick*="refresh"]');
  console.log(`🔄 找到 ${refreshButtons.length} 个刷新按钮`);
  
  const configButtons = document.querySelectorAll('button:contains("配置"), button:contains("设置")');
  console.log(`⚙️ 找到 ${configButtons.length} 个配置按钮`);
  
  const exportButtons = document.querySelectorAll('button:contains("导出"), button:contains("下载")');
  console.log(`📥 找到 ${exportButtons.length} 个导出按钮`);
  
  console.log('✅ 测试完成');
}

// 启动测试
runTest();

// 提供手动测试函数
window.testCpuToggle = testCpuChartToggle;
window.checkPage = checkPageReady;

console.log('💡 提示: 你可以手动调用 testCpuToggle() 或 checkPage() 进行测试');
