<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPU图表切换功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .toggle-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .toggle-btn:hover {
            background: #2980b9;
        }
        .toggle-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        .chart-container {
            height: 300px;
            background: #ecf0f1;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        .chart-placeholder {
            text-align: center;
            color: #7f8c8d;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #ecf0f1;
        }
        .loading {
            color: #3498db;
        }
        .success {
            color: #27ae60;
            background: #d5f4e6;
        }
        .error {
            color: #e74c3c;
            background: #fdf2f2;
        }
        .legend {
            display: flex;
            gap: 20px;
            margin-top: 10px;
            flex-wrap: wrap;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }
        .chart-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CPU图表切换功能测试</h1>
        
        <div class="card">
            <div class="header">
                <h2>CPU使用率趋势</h2>
                <button class="toggle-btn" id="toggleBtn" onclick="toggleView()">
                    详细视图
                </button>
            </div>
            
            <div class="status" id="status">准备就绪</div>
            
            <div class="chart-container">
                <div class="chart-info" id="chartInfo">
                    简化视图 - 单一CPU使用率曲线
                </div>
                <div class="chart-placeholder" id="chartPlaceholder">
                    <div>📊</div>
                    <div>CPU使用率图表</div>
                    <div style="font-size: 12px; margin-top: 10px;">
                        点击右上角按钮切换视图
                    </div>
                </div>
            </div>
            
            <div class="legend" id="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #3b82f6;"></div>
                    <span>CPU使用率</span>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>功能说明</h3>
            <ul>
                <li><strong>简化视图</strong>：显示单一的CPU总使用率曲线，使用面积图样式</li>
                <li><strong>详细视图</strong>：显示多个CPU相关指标（总使用率、用户态、系统态、I/O等待），使用线图样式</li>
                <li>切换时会显示加载状态</li>
                <li>图例会根据当前视图动态更新</li>
            </ul>
        </div>
    </div>

    <script>
        let isDetailedView = false;
        let isLoading = false;

        const toggleBtn = document.getElementById('toggleBtn');
        const status = document.getElementById('status');
        const chartInfo = document.getElementById('chartInfo');
        const legend = document.getElementById('legend');

        function updateStatus(message, type = 'info') {
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function updateLegend(isDetailed) {
            if (isDetailed) {
                legend.innerHTML = `
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #3b82f6;"></div>
                        <span>CPU总使用率</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #10b981;"></div>
                        <span>用户态</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #f59e0b;"></div>
                        <span>系统态</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #ef4444;"></div>
                        <span>I/O等待</span>
                    </div>
                `;
            } else {
                legend.innerHTML = `
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #3b82f6;"></div>
                        <span>CPU使用率</span>
                    </div>
                `;
            }
        }

        function toggleView() {
            if (isLoading) return;

            isLoading = true;
            toggleBtn.disabled = true;
            toggleBtn.textContent = '切换中...';
            updateStatus('正在切换视图...', 'loading');

            // 模拟切换过程
            setTimeout(() => {
                isDetailedView = !isDetailedView;
                
                // 更新按钮文本
                toggleBtn.textContent = isDetailedView ? '简化视图' : '详细视图';
                
                // 更新图表信息
                chartInfo.textContent = isDetailedView 
                    ? '详细视图 - 多个CPU指标曲线' 
                    : '简化视图 - 单一CPU使用率曲线';
                
                // 更新图例
                updateLegend(isDetailedView);
                
                // 更新状态
                updateStatus(
                    `已切换到${isDetailedView ? '详细' : '简化'}视图`, 
                    'success'
                );
                
                // 重置加载状态
                isLoading = false;
                toggleBtn.disabled = false;
                
                // 3秒后清除状态消息
                setTimeout(() => {
                    updateStatus('准备就绪');
                }, 3000);
                
            }, 1000); // 模拟1秒的加载时间
        }

        // 初始化
        updateLegend(false);
    </script>
</body>
</html>
