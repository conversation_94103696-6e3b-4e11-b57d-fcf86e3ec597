import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig, AxiosError } from 'axios'
import { ElMessage, ElLoading } from 'element-plus'
import { useAuthStore } from '@/store/auth'
import router from '@/router'
import { cache } from '@/utils/cache'
import { errorHandler } from '@/utils/error-handler'

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求加载状态
let loadingInstance: any = null
let requestCount = 0

// 显示加载
const showLoading = () => {
  if (requestCount === 0) {
    loadingInstance = ElLoading.service({
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
  }
  requestCount++
}

// 隐藏加载
const hideLoading = () => {
  requestCount--
  if (requestCount <= 0) {
    requestCount = 0
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
  }
}

// 生成缓存键
function generateCacheKey(config: AxiosRequestConfig | InternalAxiosRequestConfig): string {
  const { method, url, params, data } = config
  return `${method}:${url}:${JSON.stringify(params)}:${JSON.stringify(data)}`
}

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 检查缓存（仅对GET请求）
    if (config.method === 'get' && config.cache !== false) {
      const cacheKey = generateCacheKey(config)
      const cachedData = cache.get(cacheKey)
      if (cachedData) {
        // 返回缓存的数据
        return Promise.resolve({
          ...config,
          data: cachedData,
          cached: true
        } as any)
      }
    }

    // 显示加载状态
    if (config.loading !== false) {
      showLoading()
    }

    // 添加认证头
    const token = localStorage.getItem('access_token')
    if (token && config.headers) {
      console.log('设置请求认证头:', token.substring(0, 20) + '...')
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error) => {
    hideLoading()
    return Promise.reject(error)
  }
)

// Token刷新状态管理
let isRefreshing = false
let refreshPromise: Promise<string> | null = null
let failedQueue: Array<{ resolve: Function; reject: Function }> = []

// 处理队列中的请求
const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error)
    } else {
      resolve(token)
    }
  })

  failedQueue = []
}

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    hideLoading()

    // 缓存GET请求的响应（如果启用缓存）
    if (response.config.method === 'get' && response.config.cache !== false) {
      const cacheKey = generateCacheKey(response.config)
      const cacheTTL = response.config.cacheTTL || 5 * 60 * 1000 // 默认5分钟
      cache.set(cacheKey, response.data, cacheTTL)
    }

    return response.data
  },
  async (error) => {
    hideLoading()

    const { response } = error
    const originalRequest = error.config

    // 特殊处理401错误的令牌刷新逻辑
    if (response?.status === 401 && !originalRequest._retry) {
      const refreshToken = localStorage.getItem('refresh_token')

      if (!refreshToken) {
        // 没有刷新令牌，直接处理错误
        console.log('没有刷新令牌，直接处理401错误')
        errorHandler.handleAPIError(error)
        return Promise.reject(error)
      }

      // 如果已经有刷新Promise在进行中，等待它完成
      if (refreshPromise) {
        console.log('等待现有的token刷新完成...')
        try {
          const newToken = await refreshPromise
          originalRequest.headers.Authorization = `Bearer ${newToken}`
          return request(originalRequest)
        } catch (refreshError) {
          return Promise.reject(refreshError)
        }
      }

      if (isRefreshing) {
        // 如果正在刷新，将请求加入队列
        console.log('token正在刷新中，将请求加入队列')
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject })
        }).then(token => {
          originalRequest.headers.Authorization = `Bearer ${token}`
          return request(originalRequest)
        }).catch(err => {
          return Promise.reject(err)
        })
      }

      originalRequest._retry = true
      isRefreshing = true

      // 创建刷新Promise
      refreshPromise = (async () => {
        try {
          console.log('开始刷新token...')
          const authStore = useAuthStore()
          await authStore.refreshAccessToken()
          const newToken = localStorage.getItem('access_token') || localStorage.getItem('token')

          if (newToken) {
            console.log('token刷新成功')
            // 处理队列中的请求
            processQueue(null, newToken)
            return newToken
          } else {
            throw new Error('刷新令牌后未获取到新的访问令牌')
          }
        } catch (error) {
          console.error('token刷新失败:', error)
          // 刷新失败，处理队列中的请求
          processQueue(error, null)

          // 只有在刷新失败时才处理错误，避免重复错误处理
          const refreshError = error as AxiosError
          if (refreshError.response?.status !== 401) {
            errorHandler.handleAPIError(refreshError)
          } else {
            // 401错误表示认证失败，清除认证信息
            localStorage.removeItem('access_token')
            localStorage.removeItem('refresh_token')

            // 检查当前路由是否为Profile页面，如果是则不自动重定向
            const currentPath = window.location.pathname
            if (currentPath !== '/profile') {
              setTimeout(() => {
                window.location.href = '/login'
              }, 100)
            }
          }
          throw refreshError
        } finally {
          isRefreshing = false
          refreshPromise = null
        }
      })()

      try {
        const newToken = await refreshPromise
        // 重新发送原请求
        console.log('使用刷新后的token重新发送请求:', newToken.substring(0, 20) + '...')
        originalRequest.headers.Authorization = `Bearer ${newToken}`
        return request(originalRequest)
      } catch (refreshError) {
        console.error('使用刷新后的token重新发送请求失败:', refreshError)
        return Promise.reject(refreshError)
      }
    }

    // 检查是否跳过全局错误处理
    if (originalRequest.skipGlobalErrorHandler) {
      return Promise.reject(error)
    }

    // 其他错误使用统一错误处理器
    errorHandler.handleAPIError(error)
    return Promise.reject(error)
  }
)

// 扩展AxiosRequestConfig类型
declare module 'axios' {
  interface AxiosRequestConfig {
    loading?: boolean
    _retry?: boolean
    cache?: boolean
    cacheTTL?: number
    cached?: boolean
    skipGlobalErrorHandler?: boolean
  }
  
  interface InternalAxiosRequestConfig {
    loading?: boolean
    _retry?: boolean
    cache?: boolean
    cacheTTL?: number
    cached?: boolean
    skipGlobalErrorHandler?: boolean
  }
}

export default request
