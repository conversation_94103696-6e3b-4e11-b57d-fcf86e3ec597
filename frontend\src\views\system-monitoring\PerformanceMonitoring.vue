<template>
  <div class="performance-monitoring">

    <!-- 性能控制面板 -->
    <el-card class="control-panel">
      <div class="panel-header">
        <div class="header-left">
          <h3>性能监控控制台</h3>
          <!-- 数据状态指示器 -->
          <div class="data-status">
            <el-tag v-if="!store.error && store.lastUpdate" type="success" size="small">
              <el-icon><CircleCheck /></el-icon>
              数据正常
            </el-tag>
            <el-tag v-else-if="store.error" type="danger" size="small">
              <el-icon><Warning /></el-icon>
              数据异常
            </el-tag>
            <el-tag v-else type="info" size="small">
              <el-icon><Loading /></el-icon>
              加载中
            </el-tag>
          </div>
        </div>
        <div class="panel-actions">
          <el-button type="primary" @click="refreshAll" :loading="loading || store.loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button @click="exportPerformanceReport" :loading="exporting">
            <el-icon><Download /></el-icon>
            导出报告
          </el-button>

          <el-button @click="showAdvancedConfig = true">
            <el-icon><Setting /></el-icon>
            高级配置
          </el-button>
          <el-switch
            v-model="store.realTimeEnabled"
            @change="store.toggleRealTimeMonitoring"
            active-text="实时监控"
            inactive-text="暂停监控"
            style="margin-left: 16px;"
          />
        </div>
      </div>

      <!-- 系统健康状态概览 -->
      <div class="health-overview" v-if="store.systemHealth">
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="health-summary">
              <el-tag :type="getHealthStatusType()" size="large">
                <el-icon><component :is="getHealthStatusIcon()" /></el-icon>
                系统状态: {{ store.systemHealth.overall === 'healthy' ? '良好' : store.systemHealth.overall === 'warning' ? '警告' : '异常' }}
              </el-tag>
              <div class="uptime">运行时间: {{ formatUptime(store.systemHealth.uptime) }}</div>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="health-components">
              <div
                v-for="component in store.systemHealth.components"
                :key="component.name"
                :class="['health-component', component.status]"
              >
                <div class="component-icon" :style="{ backgroundColor: component.color }">
                  <el-icon><component :is="component.icon" /></el-icon>
                </div>
                <div class="component-info">
                  <div class="component-name">{{ component.name }}</div>
                  <div class="component-status">{{ component.statusText }}</div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 监控配置 -->
      <div class="monitoring-config">
        <el-form :model="config" inline>
          <el-form-item label="刷新间隔">
            <el-select v-model="config.refreshInterval" @change="updateRefreshInterval">
              <el-option label="5秒" :value="5" />
              <el-option label="10秒" :value="10" />
              <el-option label="30秒" :value="30" />
              <el-option label="1分钟" :value="60" />
            </el-select>
          </el-form-item>

          <el-form-item label="数据保留">
            <el-select v-model="config.dataRetention">
              <el-option label="最近1小时" value="1h" />
              <el-option label="最近6小时" value="6h" />
              <el-option label="最近24小时" value="24h" />
              <el-option label="最近7天" value="7d" />
            </el-select>
          </el-form-item>

          <el-form-item label="告警阈值">
            <el-input-number
              v-model="config.alertThreshold"
              :min="50"
              :max="95"
              :step="5"
              controls-position="right"
            />
            <span style="margin-left: 8px;">%</span>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" size="small" @click="showAdvancedConfig = true">
              <el-icon><Setting /></el-icon>
              高级配置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 钻取分析提示 -->
    <el-alert
      v-if="focusedMetric"
      :title="`正在查看 ${route.query.metric} 的详细分析`"
      type="info"
      :closable="true"
      @close="focusedMetric = null"
      style="margin-bottom: 24px;"
    >
      <template #default>
        <p>您已从概览页面钻取到此指标的详细视图。可以查看更多相关的性能数据和历史趋势。</p>
      </template>
    </el-alert>

    <!-- 错误状态提示 -->
    <el-alert
      v-if="store.error && !store.loading"
      :title="store.error"
      type="error"
      :closable="false"
      style="margin-bottom: 24px;"
      show-icon
    >
      <template #default>
        <p>性能数据获取失败，可能是网络连接问题或服务暂时不可用。</p>
        <div style="margin-top: 12px;">
          <el-button size="small" type="primary" @click="refreshAll" :loading="loading">
            <el-icon><Refresh /></el-icon>
            重试
          </el-button>
          <el-button size="small" @click="store.error = null">
            <el-icon><Close /></el-icon>
            忽略
          </el-button>
        </div>
      </template>
    </el-alert>

    <!-- 快速统计和前端性能 -->
    <el-row :gutter="24" style="margin-top: 24px;">
      <!-- 快速统计 -->
      <el-col :xs="24" :lg="12">
        <el-card class="quick-stats-card">
          <template #header>
            <span>业务统计</span>
          </template>
          <div class="quick-stats" v-if="store.quickStats">
            <div class="stat-item">
              <div class="stat-icon" style="background-color: #3b82f6;">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatNumber(store.quickStats.onlineUsers) }}</div>
                <div class="stat-label">在线用户</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon" style="background-color: #10b981;">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatNumber(store.quickStats.apiRequestsPerMinute) }}</div>
                <div class="stat-label">API请求/分钟</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon" style="background-color: #ef4444;">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ store.quickStats.errorRate.toFixed(2) }}%</div>
                <div class="stat-label">错误率</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 前端性能 -->
      <el-col :xs="24" :lg="12">
        <el-card class="frontend-performance-card">
          <template #header>
            <div class="card-header">
              <span>前端性能</span>
              <el-button size="small" @click="refreshFrontendMetrics">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <div class="frontend-metrics" v-if="store.frontendMetrics">
            <div class="metric-row">
              <span class="metric-label">页面加载时间</span>
              <span class="metric-value">{{ store.frontendMetrics.pageLoad.loadComplete }}ms</span>
            </div>
            <div class="metric-row">
              <span class="metric-label">JS内存使用</span>
              <span class="metric-value">{{ store.frontendMetrics.memory.usedJSHeapSize }}MB</span>
            </div>
            <div class="metric-row">
              <span class="metric-label">缓存命中率</span>
              <span class="metric-value">{{ store.frontendMetrics.cache.hitRate }}%</span>
            </div>
            <div class="metric-row">
              <span class="metric-label">API响应时间</span>
              <span class="metric-value">{{ store.frontendMetrics.api.averageResponseTime }}ms</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 性能图表 -->
    <div class="performance-charts">
      <el-row :gutter="24">
        <!-- CPU使用率趋势 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>CPU使用率趋势</span>
                <el-button size="small" @click="toggleCpuChart" :loading="loading">
                  {{ showCpuDetails ? '简化视图' : '详细视图' }}
                </el-button>
              </div>
            </template>
            <div class="chart-container">
              <LineChart
                :data="currentCpuChartData"
                :x-axis-data="cpuChartLabels"
                height="300px"
                :show-legend="cpuChartConfig.showLegend"
                :show-grid="cpuChartConfig.showGrid"
                :show-tooltip="cpuChartConfig.showTooltip"
                :smooth="cpuChartConfig.smooth"
                :area="cpuChartConfig.area"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 内存使用情况 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <span>内存使用分布</span>
            </template>
            <div class="chart-container">
              <DoughnutChart
                :data="memoryChartData"
                height="300px"
                :show-legend="false"
              />
              <div class="memory-details">
                <div v-for="item in memoryDetails" :key="item.label" class="memory-item">
                  <div class="memory-color" :style="{ backgroundColor: item.color }"></div>
                  <div class="memory-info">
                    <div class="memory-label">{{ item.label }}</div>
                    <div class="memory-value">{{ item.value }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="24" style="margin-top: 24px;">
        <!-- 网络流量监控 -->
        <el-col :xs="24" :lg="16">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>网络流量监控</span>
                <el-select v-model="networkTimeRange" size="small" @change="updateNetworkChart">
                  <el-option label="最近1小时" value="1h" />
                  <el-option label="最近6小时" value="6h" />
                  <el-option label="最近24小时" value="24h" />
                </el-select>
              </div>
            </template>
            <div class="chart-container">
              <AreaChart
                :data="networkChartData"
                height="300px"
                :show-legend="true"
                :show-grid="true"
                :show-tooltip="true"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 系统负载 -->
        <el-col :xs="24" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <span>系统负载</span>
            </template>
            <div class="chart-container">
              <div class="load-gauges">
                <div v-for="load in systemLoads" :key="load.label" class="load-gauge">
                  <div class="gauge-container">
                    <div 
                      :style="{
                        width: '80px',
                        height: '80px',
                        border: `3px solid ${getLoadColor(load.value)}`,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        margin: '0 auto',
                        background: getLoadBackgroundColor(load.value)
                      }"
                    >
                      <span 
                        :style="{
                          fontSize: '14px',
                          fontWeight: 'bold',
                          color: getLoadColor(load.value)
                        }"
                      >
                        {{ load.value }}%
                      </span>
                    </div>
                  </div>
                  <div class="gauge-label">{{ load.label }}</div>
                </div>
              </div>
              
              <!-- 加载状态提示 -->
              <div v-if="loading" class="loading-indicator">
                <el-icon class="is-loading"><Loading /></el-icon>
                <span>正在获取系统负载数据...</span>
              </div>
              
              <!-- 无数据提示 -->
              <div v-else-if="systemLoads.every(load => load.value === 0)" class="no-data-indicator">
                <el-icon><Warning /></el-icon>
                <span>暂无系统负载数据</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 性能警告 -->
    <el-card v-if="performanceAlerts.length > 0" class="alerts-card">
      <template #header>
        <div class="alerts-header">
          <span>性能警告</span>
          <el-button size="small" @click="clearAllAlerts">
            <el-icon><Delete /></el-icon>
            清除所有
          </el-button>
        </div>
      </template>
      
      <div class="alerts-list">
        <el-alert
          v-for="(alert, index) in performanceAlerts"
          :key="index"
          :title="alert.title"
          :description="alert.description"
          :type="alert.type"
          :closable="true"
          @close="removeAlert(index)"
          style="margin-bottom: 12px;"
        >
          <template #default>
            <div class="alert-content">
              <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
              <div class="alert-metric">{{ alert.metric }}</div>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>

    <!-- 性能历史记录 -->
    <el-card class="history-card">
      <template #header>
        <div class="history-header">
          <span>性能历史记录</span>
          <div class="history-controls">
            <el-date-picker
              v-model="performanceHistoryDateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              size="small"
              @change="loadHistoryData"
            />
            <el-button size="small" @click="exportHistoryData">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="performanceHistory" style="width: 100%" v-loading="historyLoading" element-loading-text="正在加载历史数据...">
        <el-table-column prop="timestamp" label="时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="cpu" label="CPU使用率" width="120">
          <template #default="{ row }">
            <el-tag :type="getMetricTagType(row.cpu)">{{ row.cpu }}%</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="memory" label="内存使用率" width="120">
          <template #default="{ row }">
            <el-tag :type="getMetricTagType(row.memory)">{{ row.memory }}%</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="disk" label="磁盘使用率" width="120">
          <template #default="{ row }">
            <el-tag :type="getMetricTagType(row.disk)">{{ row.disk }}%</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="network" label="网络延迟" width="120">
          <template #default="{ row }">
            {{ row.network }}ms
          </template>
        </el-table-column>
        <el-table-column prop="load" label="系统负载" width="120">
          <template #default="{ row }">
            {{ row.load }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getOverallStatusType(row)">
              {{ getOverallStatus(row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row, $index }">
            <el-button size="small" text @click="viewHistoryDetails(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="history-pagination">
        <el-pagination
          v-model:current-page="historyPagination.currentPage"
          v-model:page-size="historyPagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="historyTotal"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleHistorySizeChange"
          @current-change="handleHistoryCurrentChange"
        />
      </div>
    </el-card>

    <!-- 高级配置对话框 -->
    <el-dialog
      v-model="showAdvancedConfig"
      title="性能监控高级配置"
      width="900px"
      :before-close="handleAdvancedConfigClose"
    >
      <el-form :model="advancedConfig" ref="advancedConfigFormRef" label-width="140px">
        <el-divider content-position="left">监控配置</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="启用性能监控">
              <el-switch v-model="advancedConfig.enableMonitoring" />
              <span style="margin-left: 8px; color: #999;">开启系统性能实时监控</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据刷新间隔" prop="refreshInterval">
              <el-select v-model="advancedConfig.refreshInterval" placeholder="选择刷新间隔">
                <el-option label="5秒" :value="5" />
                <el-option label="10秒" :value="10" />
                <el-option label="30秒" :value="30" />
                <el-option label="1分钟" :value="60" />
                <el-option label="5分钟" :value="300" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="监控指标">
          <el-checkbox-group v-model="advancedConfig.monitoringMetrics">
            <el-checkbox label="cpu">CPU使用率</el-checkbox>
            <el-checkbox label="memory">内存使用率</el-checkbox>
            <el-checkbox label="disk">磁盘使用率</el-checkbox>
            <el-checkbox label="network">网络状态</el-checkbox>
            <el-checkbox label="database">数据库性能</el-checkbox>
            <el-checkbox label="api">API响应时间</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-divider content-position="left">告警设置</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="CPU告警阈值" prop="cpuThreshold">
              <el-input-number v-model="advancedConfig.alertThresholds.cpu" :min="50" :max="95" />
              <span style="margin-left: 8px;">%</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="内存告警阈值" prop="memoryThreshold">
              <el-input-number v-model="advancedConfig.alertThresholds.memory" :min="50" :max="95" />
              <span style="margin-left: 8px;">%</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="磁盘告警阈值" prop="diskThreshold">
              <el-input-number v-model="advancedConfig.alertThresholds.disk" :min="70" :max="95" />
              <span style="margin-left: 8px;">%</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="响应时间告警" prop="responseTimeThreshold">
              <el-input-number v-model="advancedConfig.alertThresholds.responseTime" :min="100" :max="5000" />
              <span style="margin-left: 8px;">ms</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="告警通知方式">
          <el-checkbox-group v-model="advancedConfig.alertMethods">
            <el-checkbox label="email">邮件通知</el-checkbox>
            <el-checkbox label="sms">短信通知</el-checkbox>
            <el-checkbox label="webhook">Webhook</el-checkbox>
            <el-checkbox label="browser">浏览器通知</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-divider content-position="left">历史数据</el-divider>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="数据保留期">
              <el-select v-model="advancedConfig.dataRetentionDays" placeholder="选择保留期">
                <el-option label="7天" :value="7" />
                <el-option label="30天" :value="30" />
                <el-option label="90天" :value="90" />
                <el-option label="180天" :value="180" />
                <el-option label="1年" :value="365" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="数据采样间隔">
              <el-select v-model="advancedConfig.samplingInterval" placeholder="选择采样间隔">
                <el-option label="1分钟" :value="1" />
                <el-option label="5分钟" :value="5" />
                <el-option label="15分钟" :value="15" />
                <el-option label="1小时" :value="60" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作">
              <el-button type="primary" @click="exportPerformanceData" size="small">导出数据</el-button>
              <el-button type="warning" @click="clearPerformanceData" size="small">清理数据</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetAdvancedConfig">重置</el-button>
          <el-button @click="showAdvancedConfig = false">取消</el-button>
          <el-button type="primary" @click="saveAdvancedConfig" :loading="advancedConfigSaving">保存配置</el-button>
          <el-button type="info" @click="testAlertSettings">测试告警</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Download, ArrowUp, ArrowDown, Delete, View,
  Monitor, DataBoard, TrendCharts, Connection, Setting,
  User, Warning, CircleCheck, CircleClose, Loading, Close
} from '@element-plus/icons-vue'
import { LineChart, DoughnutChart, AreaChart } from '@/components/charts'
import { usePerformanceStore } from '@/stores/performance'
import { performanceService } from '@/services/performanceService'
import ChartDataAdapter from '@/utils/chartDataAdapter'
import type { MetricCard, PerformanceAlert } from '@/types/performance'
import type { SeriesData } from '@/components/charts/LineChart.vue'

// 使用性能监控store和路由
const store = usePerformanceStore()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const showCpuDetails = ref(false)
const networkTimeRange = ref('6h')
// 使用 store 的 realTimeEnabled，不需要本地变量

// 配置
const config = reactive({
  refreshInterval: 10,
  dataRetention: '6h',
  alertThreshold: 80
})

// 高级配置相关
const showAdvancedConfig = ref(false)
const advancedConfigSaving = ref(false)
const advancedConfigFormRef = ref()

const advancedConfig = reactive({
  enableMonitoring: true,
  refreshInterval: 30,
  monitoringMetrics: ['cpu', 'memory', 'disk', 'network', 'api'],
  alertThresholds: {
    cpu: 80,
    memory: 85,
    disk: 90,
    responseTime: 1000
  },
  alertMethods: ['email', 'browser'],
  dataRetentionDays: 30,
  samplingInterval: 5
})

// 性能指标数据
const performanceMetrics = reactive([
  {
    label: 'CPU使用率',
    value: 0,
    unit: '%',
    change: 0,
    color: '#3b82f6',
    icon: 'Monitor'
  },
  {
    label: '内存使用',
    value: 0,
    unit: '%',
    change: 0,
    color: '#10b981',
    icon: 'DataBoard'
  },
  {
    label: '磁盘使用',
    value: 0,
    unit: '%',
    change: 0,
    color: '#f59e0b',
    icon: 'TrendCharts'
  },
  {
    label: '网络延迟',
    value: 0,
    unit: 'ms',
    change: 0,
    color: '#8b5cf6',
    icon: 'Connection'
  }
])

// CPU图表数据
const cpuChartData = ref([
  {
    name: 'CPU使用率',
    data: [] as number[],
    color: '#3b82f6',
    type: 'line' as const,
    smooth: true,
    area: true
  }
] as SeriesData[])

// CPU详细图表数据（用于详细视图）
const cpuDetailedChartData = ref([
  {
    name: 'CPU总使用率',
    data: [] as number[],
    color: '#3b82f6',
    type: 'line' as const,
    smooth: true,
    area: false
  },
  {
    name: '用户态',
    data: [] as number[],
    color: '#10b981',
    type: 'line' as const,
    smooth: true,
    area: false
  },
  {
    name: '系统态',
    data: [] as number[],
    color: '#f59e0b',
    type: 'line' as const,
    smooth: true,
    area: false
  },
  {
    name: 'I/O等待',
    data: [] as number[],
    color: '#ef4444',
    type: 'line' as const,
    smooth: true,
    area: false
  }
] as SeriesData[])

const cpuChartLabels = ref([] as string[])

// 计算属性：根据视图状态返回相应的CPU图表数据
const currentCpuChartData = computed(() => {
  return showCpuDetails.value ? cpuDetailedChartData.value : cpuChartData.value
})

// 计算属性：根据视图状态返回相应的图表配置
const cpuChartConfig = computed(() => {
  return {
    showLegend: showCpuDetails.value,
    showGrid: true,
    showTooltip: true,
    smooth: true,
    area: !showCpuDetails.value // 简化视图显示面积图，详细视图显示线图
  }
})

// CPU图表选项已移除，使用LineChart组件的内置配置

// 内存图表数据
const memoryChartData = ref({
  labels: ['已使用', '缓存', '缓冲区', '可用'],
  datasets: [{
    data: [0, 0, 0, 100],
    backgroundColor: [
      '#ef4444',
      '#f59e0b',
      '#3b82f6',
      '#10b981'
    ],
    borderWidth: 2,
    borderColor: ['#ffffff', '#ffffff', '#ffffff', '#ffffff']
  }]
})

// 内存图表选项已移除，使用DoughnutChart组件的内置配置

const memoryDetails = ref([
  { label: '已使用', value: '0%', color: '#ef4444' },
  { label: '缓存', value: '0%', color: '#f59e0b' },
  { label: '缓冲区', value: '0%', color: '#3b82f6' },
  { label: '可用', value: '100%', color: '#10b981' }
])

// 网络图表数据 - 初始化为测试数据
const networkChartData = ref({
  labels: ['10:00', '10:05', '10:10', '10:15', '10:20'],
  datasets: [
    {
      label: '入站流量',
      data: [5, 8, 6, 9, 7],
      borderColor: '#10b981',
      backgroundColor: 'rgba(16, 185, 129, 0.2)',
      tension: 0.4,
      fill: true
    },
    {
      label: '出站流量',
      data: [3, 5, 4, 6, 5],
      borderColor: '#3b82f6',
      backgroundColor: 'rgba(59, 130, 246, 0.2)',
      tension: 0.4,
      fill: true
    }
  ]
})

// 网络图表选项已移除，使用AreaChart组件的内置配置

// 系统负载数据 - 初始化为0值
const systemLoads = ref([
  { label: '1分钟', value: 0 },
  { label: '5分钟', value: 0 },
  { label: '15分钟', value: 0 }
])

// 性能警告
const performanceAlerts = ref<PerformanceAlert[]>([
  {
    id: '1',
    title: 'CPU使用率过高',
    description: 'CPU使用率已达到85%，建议检查系统负载',
    type: 'warning',
    timestamp: new Date(),
    metric: 'CPU: 85%'
  },
  {
    id: '2',
    title: '内存使用率警告',
    description: '内存使用率超过阈值，可能影响系统性能',
    type: 'error',
    timestamp: new Date(Date.now() - 300000),
    metric: 'Memory: 92%'
  }
])

// 性能历史记录
interface HistoryDataItem {
  timestamp: Date
  cpu: number
  memory: number
  disk: number
  network: number
  load: string
}

const performanceHistory = ref<HistoryDataItem[]>([])
const historyLoading = ref(false)
const performanceHistoryDateRange = ref<[Date, Date] | []>([])
const fullHistoryData = ref<HistoryDataItem[]>([]) // 存储完整的历史数据

const historyPagination = reactive({
  currentPage: 1,
  pageSize: 20
})

const historyTotal = ref(0)

// 加载真实性能数据
const loadRealPerformanceData = async () => {
  try {
    loading.value = true

    // 获取实时指标
    const realTimeMetrics = await performanceService.getRealTimeMetrics()

    // 使用适配器更新性能指标
    const adaptedMetrics = ChartDataAdapter.adaptPerformanceMetrics(realTimeMetrics)
    adaptedMetrics.forEach((metric, index) => {
      if (performanceMetrics[index]) {
        performanceMetrics[index].value = metric.value
        performanceMetrics[index].change = metric.change
      }
    })
    
    // 获取历史数据
    const historicalData = await performanceService.getHistoricalData('6h')

    // 使用适配器更新CPU图表数据
    const cpuChartAdapted = ChartDataAdapter.adaptCpuDataForLineChart(historicalData)
    cpuChartLabels.value = cpuChartAdapted.labels
    cpuChartData.value = cpuChartAdapted.data
    
    // 使用适配器更新内存图表数据
    const memoryChartAdapted = ChartDataAdapter.adaptMemoryDataForDoughnutChart(realTimeMetrics)
    memoryChartData.value = memoryChartAdapted.data
    memoryDetails.value = memoryChartAdapted.details
    
    // 使用适配器更新网络图表数据
    const networkChartAdapted = ChartDataAdapter.adaptNetworkDataForAreaChart(historicalData)
    networkChartData.value = networkChartAdapted
    console.log('网络图表数据已更新:', networkChartAdapted)

    // 使用适配器更新系统负载数据
    systemLoads.value = ChartDataAdapter.adaptSystemLoadData(realTimeMetrics)
    
    
    // 获取系统健康状态和告警
    const healthStatus = await performanceService.getSystemHealth()
    const alerts = await performanceService.getAlerts()
    
    // 更新性能告警
    if (alerts && alerts.length > 0) {
      performanceAlerts.value = alerts.map(alert => ({
        id: alert.id,
        title: alert.title,
        description: alert.description,
        type: alert.type,
        timestamp: new Date(alert.timestamp),
        metric: alert.metric
      }))
    }
    
    console.log('真实性能数据加载成功')
  } catch (error) {
    console.error('加载真实性能数据失败:', error)
    // 保持现有的模拟数据
    throw error
  } finally {
    loading.value = false
  }
}

// 方法实现
const getMetricStatus = (value: number) => {
  if (value >= 90) return 'error'
  if (value >= 70) return 'warning'
  if (value >= 50) return 'normal'
  return 'excellent'
}





const getLoadColor = (value: number) => {
  if (value >= 80) return '#ef4444'
  if (value >= 60) return '#f59e0b'
  if (value > 0) return '#10b981'
  return '#d1d5db' // 灰色表示无数据
}

const getLoadBackgroundColor = (value: number) => {
  if (value >= 80) return '#fef2f2'
  if (value >= 60) return '#fffbeb'
  if (value > 0) return '#f0f9ff'
  return '#f9fafb' // 浅灰色背景表示无数据
}

const getMetricTagType = (value: number) => {
  if (value >= 90) return 'danger'
  if (value >= 70) return 'warning'
  return 'success'
}

const getOverallStatus = (row: any) => {
  const maxValue = Math.max(row.cpu, row.memory, row.disk)
  if (maxValue >= 90) return '异常'
  if (maxValue >= 70) return '警告'
  return '正常'
}

const getOverallStatusType = (row: any) => {
  const status = getOverallStatus(row)
  switch (status) {
    case '异常':
      return 'danger'
    case '警告':
      return 'warning'
    default:
      return 'success'
  }
}

const formatTime = (time: Date) => {
  return time.toLocaleString()
}

// 事件处理方法
const refreshPerformanceData = async (showMessage = true) => {
  loading.value = true
  try {
    // 尝试加载真实数据
    await loadRealPerformanceData()
    if (showMessage) {
      ElMessage.success('性能数据刷新成功')
    }
  } catch (error) {
    console.warn('真实数据获取失败:', error)

    // 降级处理：保持当前数据不变，只更新图表
    try {
      await updateChartData()
      if (showMessage) {
        ElMessage.warning('部分性能数据获取失败，显示可用数据')
      }
    } catch (chartError) {
      console.error('图表数据更新失败:', chartError)
      if (showMessage) {
        ElMessage.error('性能数据刷新失败')
      }
    }
  } finally {
    loading.value = false
  }
}

const exportPerformanceReport = async () => {
  exporting.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 模拟导出报告
    const reportData = {
      timestamp: new Date().toISOString(),
      metrics: performanceMetrics,
      history: performanceHistory.value,
      alerts: performanceAlerts.value
    }

    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-report-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    ElMessage.success('性能报告导出成功')
  } catch (error) {
    ElMessage.error('报告导出失败')
  } finally {
    exporting.value = false
  }
}

const toggleRealTime = (enabled: boolean) => {
  if (enabled) {
    startRealTimeMonitoring()
    ElMessage.success('实时监控已开启')
  } else {
    stopRealTimeMonitoring()
    ElMessage.info('实时监控已暂停')
  }
}

const updateRefreshInterval = () => {
  if (store.realTimeEnabled) {
    stopRealTimeMonitoring()
    startRealTimeMonitoring()
  }
  ElMessage.success(`刷新间隔已更新为${config.refreshInterval}秒`)
}

const toggleCpuChart = async () => {
  const previousState = showCpuDetails.value
  showCpuDetails.value = !showCpuDetails.value

  try {
    // 显示加载状态
    loading.value = true

    if (showCpuDetails.value) {
      // 切换到详细视图 - 生成详细的CPU数据
      await generateDetailedCpuData()
      ElMessage.success('已切换到详细视图')
    } else {
      // 切换到简化视图 - 使用基本的CPU数据
      await generateCpuData()
      ElMessage.success('已切换到简化视图')
    }
  } catch (error) {
    console.error('切换CPU图表视图失败:', error)
    // 恢复之前的状态
    showCpuDetails.value = previousState
    ElMessage.error('切换视图失败，请重试')
  } finally {
    loading.value = false
  }
}

const updateNetworkChart = async () => {
  try {
    await generateNetworkData()
    ElMessage.info(`网络图表已更新为${networkTimeRange.value}数据`)
  } catch (error) {
    console.error('更新网络图表失败:', error)
    ElMessage.error('网络图表更新失败')
  }
}

const clearAllAlerts = () => {
  performanceAlerts.value = []
  ElMessage.success('所有警告已清除')
}

const removeAlert = (index: number) => {
  performanceAlerts.value.splice(index, 1)
  ElMessage.success('警告已移除')
}

const loadHistoryData = async (showMessage = true) => {
  try {
    historyLoading.value = true
    
    // 确定时间范围
    let timeRange = '24h' // 默认24小时
    if (performanceHistoryDateRange.value && performanceHistoryDateRange.value.length === 2) {
      const startTime = new Date(performanceHistoryDateRange.value[0])
      const endTime = new Date(performanceHistoryDateRange.value[1])
      const diffHours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60)
      
      if (diffHours <= 1) timeRange = '1h'
      else if (diffHours <= 6) timeRange = '6h'
      else timeRange = '24h'
    }
    
    // 获取历史数据
    const historicalData = await performanceService.getHistoricalData(timeRange)
    
    // 转换数据格式为表格需要的格式
    const tableData = historicalData.dataPoints.map(point => ({
      timestamp: new Date(point.timestamp),
      cpu: point.cpu,
      memory: point.memory,
      disk: point.disk,
      network: point.network,
      load: (point.cpu / 100 * 2).toFixed(1) // 基于CPU使用率估算系统负载
    }))
    
    // 存储完整数据
    fullHistoryData.value = tableData
    historyTotal.value = tableData.length
    
    // 应用分页显示
    updateHistoryPagination()
    
    if (showMessage) {
      ElMessage.success(`已加载 ${tableData.length} 条历史数据`)
    }
  } catch (error) {
    console.error('加载历史数据失败:', error)
    if (showMessage) {
      ElMessage.error('加载历史数据失败，请稍后重试')
    }
    performanceHistory.value = []
    fullHistoryData.value = []
    historyTotal.value = 0
  } finally {
    historyLoading.value = false
  }
}

// 更新分页显示
const updateHistoryPagination = () => {
  const startIndex = (historyPagination.currentPage - 1) * historyPagination.pageSize
  const endIndex = startIndex + historyPagination.pageSize
  performanceHistory.value = fullHistoryData.value.slice(startIndex, endIndex)
}

const exportHistoryData = async () => {
  try {
    if (fullHistoryData.value.length === 0) {
      ElMessage.warning('暂无历史数据可导出')
      return
    }
    
    const exportData = {
      exportTime: new Date().toISOString(),
      totalRecords: fullHistoryData.value.length,
      timeRange: performanceHistoryDateRange.value.length === 2 ?
        `${performanceHistoryDateRange.value[0]} 至 ${performanceHistoryDateRange.value[1]}` : '默认24小时',
      data: fullHistoryData.value
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-history-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    ElMessage.success(`已导出 ${fullHistoryData.value.length} 条历史数据`)
  } catch (error) {
    console.error('导出历史数据失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  }
}

const viewHistoryDetails = (row: any) => {
  ElMessage.info(`查看${formatTime(row.timestamp)}的详细性能数据`)
  // TODO: 可以打开详情对话框显示更多信息
}

const handleHistorySizeChange = (size: number) => {
  historyPagination.pageSize = size
  historyPagination.currentPage = 1
  updateHistoryPagination()
}

const handleHistoryCurrentChange = (page: number) => {
  historyPagination.currentPage = page
  updateHistoryPagination()
}

// 数据生成方法
const updateChartData = async () => {
  try {
    await generateCpuData()
    await generateNetworkData()
    await updateSystemLoads()
  } catch (error) {
    console.error('更新图表数据失败:', error)
  }
}

const generateCpuData = async () => {
  try {
    // 尝试获取真实的历史CPU数据
    const historicalData = await performanceService.getHistoricalData('1h')

    // 使用适配器转换数据
    const cpuChartAdapted = ChartDataAdapter.safeAdapt(
      () => ChartDataAdapter.adaptCpuDataForLineChart(historicalData),
      {
        data: [{
          name: 'CPU使用率',
          data: [],
          color: '#3b82f6',
          type: 'line' as const,
          smooth: true,
          area: true
        }],
        labels: []
      },
      '获取CPU图表数据失败'
    )

    cpuChartLabels.value = cpuChartAdapted.labels
    cpuChartData.value = cpuChartAdapted.data
  } catch (error) {
    console.warn('获取真实CPU数据失败:', error)
    // 使用空数据，让用户知道数据获取失败
    cpuChartLabels.value = []
    cpuChartData.value[0].data = []
  }
}

// 生成详细的CPU数据
const generateDetailedCpuData = async () => {
  try {
    // 获取历史数据
    const historicalData = await performanceService.getHistoricalData('1h')

    if (historicalData && historicalData.cpu && historicalData.cpu.length > 0) {
      // 基于真实CPU数据生成模拟的详细数据
      const cpuTotal = historicalData.cpu
      const labels = historicalData.timestamps.map(timestamp => {
        const date = new Date(timestamp)
        return date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      })

      // 生成模拟的用户态、系统态、I/O等待数据
      const userMode = cpuTotal.map(total => Math.max(0, total * (0.6 + Math.random() * 0.2))) // 60-80%的CPU用于用户态
      const systemMode = cpuTotal.map(total => Math.max(0, total * (0.15 + Math.random() * 0.1))) // 15-25%用于系统态
      const ioWait = cpuTotal.map(total => Math.max(0, total * (0.05 + Math.random() * 0.1))) // 5-15%用于I/O等待

      cpuChartLabels.value = labels
      cpuDetailedChartData.value = [
        {
          name: 'CPU总使用率',
          data: cpuTotal,
          color: '#3b82f6',
          type: 'line' as const,
          smooth: true,
          area: false
        },
        {
          name: '用户态',
          data: userMode,
          color: '#10b981',
          type: 'line' as const,
          smooth: true,
          area: false
        },
        {
          name: '系统态',
          data: systemMode,
          color: '#f59e0b',
          type: 'line' as const,
          smooth: true,
          area: false
        },
        {
          name: 'I/O等待',
          data: ioWait,
          color: '#ef4444',
          type: 'line' as const,
          smooth: true,
          area: false
        }
      ]
    } else {
      // 如果没有真实数据，生成模拟数据
      generateMockDetailedCpuData()
    }
  } catch (error) {
    console.warn('获取详细CPU数据失败:', error)
    generateMockDetailedCpuData()
  }
}

// 生成模拟的详细CPU数据
const generateMockDetailedCpuData = () => {
  const now = new Date()
  const labels = []
  const cpuTotal = []
  const userMode = []
  const systemMode = []
  const ioWait = []

  // 生成过去1小时的数据点（每5分钟一个点）
  for (let i = 11; i >= 0; i--) {
    const time = new Date(now.getTime() - i * 5 * 60 * 1000)
    labels.push(time.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    }))

    const total = Math.round(20 + Math.random() * 60) // 20-80%的总CPU使用率
    const user = Math.round(total * (0.6 + Math.random() * 0.2)) // 60-80%用于用户态
    const system = Math.round(total * (0.15 + Math.random() * 0.1)) // 15-25%用于系统态
    const io = Math.round(total * (0.05 + Math.random() * 0.1)) // 5-15%用于I/O等待

    cpuTotal.push(total)
    userMode.push(user)
    systemMode.push(system)
    ioWait.push(io)
  }

  cpuChartLabels.value = labels
  cpuDetailedChartData.value = [
    {
      name: 'CPU总使用率',
      data: cpuTotal,
      color: '#3b82f6',
      type: 'line' as const,
      smooth: true,
      area: false
    },
    {
      name: '用户态',
      data: userMode,
      color: '#10b981',
      type: 'line' as const,
      smooth: true,
      area: false
    },
    {
      name: '系统态',
      data: systemMode,
      color: '#f59e0b',
      type: 'line' as const,
      smooth: true,
      area: false
    },
    {
      name: 'I/O等待',
      data: ioWait,
      color: '#ef4444',
      type: 'line' as const,
      smooth: true,
      area: false
    }
  ]
}

const generateNetworkData = async () => {
  try {
    // 尝试获取真实的历史网络数据
    const historicalData = await performanceService.getHistoricalData(networkTimeRange.value)

    // 使用适配器转换数据
    const networkChartAdapted = ChartDataAdapter.safeAdapt(
      () => ChartDataAdapter.adaptNetworkDataForAreaChart(historicalData),
      {
        labels: [],
        datasets: [
          {
            label: '入站流量',
            data: [],
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.2)',
            tension: 0.4,
            fill: true
          },
          {
            label: '出站流量',
            data: [],
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.2)',
            tension: 0.4,
            fill: true
          }
        ]
      },
      '获取网络图表数据失败'
    )

    networkChartData.value = networkChartAdapted
  } catch (error) {
    console.warn('获取真实网络数据失败:', error)
    // 使用空数据
    networkChartData.value.labels = []
    networkChartData.value.datasets[0].data = []
    networkChartData.value.datasets[1].data = []
  }
}

const updateSystemLoads = async () => {
  try {
    // 尝试获取真实的CPU数据来计算系统负载
    const realTimeMetrics = await performanceService.getRealTimeMetrics()

    // 使用适配器转换系统负载数据
    systemLoads.value = ChartDataAdapter.safeAdapt(
      () => ChartDataAdapter.adaptSystemLoadData(realTimeMetrics),
      [
        { label: '1分钟', value: 0 },
        { label: '5分钟', value: 0 },
        { label: '15分钟', value: 0 }
      ],
      '获取系统负载数据失败'
    )
  } catch (error) {
    console.warn('获取真实系统负载失败:', error)
    // 获取失败时保持0值
    systemLoads.value = [
      { label: '1分钟', value: 0 },
      { label: '5分钟', value: 0 },
      { label: '15分钟', value: 0 }
    ]
  }
}

// 实时监控 - 使用Store统一管理，移除组件内重复定时器
const startRealTimeMonitoring = () => {
  // 不再在组件内创建独立定时器，依赖Store的实时更新
  console.log('实时监控已通过Store统一管理')
}

const stopRealTimeMonitoring = () => {
  // 不再在组件内管理定时器，依赖Store的实时更新
  console.log('实时监控停止已通过Store统一管理')
}

// 高级配置相关方法
const handleAdvancedConfigClose = () => {
  showAdvancedConfig.value = false
}

const saveAdvancedConfig = async () => {
  try {
    advancedConfigSaving.value = true
    // 模拟保存配置
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 更新基础配置
    config.refreshInterval = advancedConfig.refreshInterval

    ElMessage.success('性能监控配置保存成功')
    showAdvancedConfig.value = false
  } catch (error) {
    ElMessage.error('配置保存失败')
  } finally {
    advancedConfigSaving.value = false
  }
}

const resetAdvancedConfig = () => {
  advancedConfig.enableMonitoring = true
  advancedConfig.refreshInterval = 30
  advancedConfig.monitoringMetrics = ['cpu', 'memory', 'disk', 'network', 'api']
  advancedConfig.alertThresholds = {
    cpu: 80,
    memory: 85,
    disk: 90,
    responseTime: 1000
  }
  advancedConfig.alertMethods = ['email', 'browser']
  advancedConfig.dataRetentionDays = 30
  advancedConfig.samplingInterval = 5
  ElMessage.success('配置已重置')
}

const exportPerformanceData = async () => {
  try {
    // 模拟导出性能数据
    const data = {
      metrics: performanceMetrics,
      alerts: performanceAlerts.value,
      config: advancedConfig,
      exportTime: new Date().toISOString()
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-data-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    ElMessage.success('性能数据导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const clearPerformanceData = async () => {
  try {
    await ElMessageBox.confirm('确定要清理历史性能数据吗？此操作不可恢复。', '确认清理', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 模拟清理数据
    performanceAlerts.value = []
    ElMessage.success('历史数据已清理')
  } catch {
    // 用户取消
  }
}

const testAlertSettings = async () => {
  try {
    // 模拟测试告警
    const testAlert: PerformanceAlert = {
      id: Date.now().toString(),
      title: '测试告警',
      description: '这是一条测试告警消息，用于验证告警配置是否正常工作',
      type: 'info',
      timestamp: new Date(),
      metric: '测试指标: 100%'
    }

    performanceAlerts.value.unshift(testAlert)
    ElMessage.success('测试告警已发送')
  } catch (error) {
    ElMessage.error('测试告警失败')
  }
}



// 新增的方法
const refreshAll = async () => {
  try {
    loading.value = true

    // 并行刷新Store数据和本地图表数据
    const [storeResult, localResult] = await Promise.allSettled([
      store.refreshAll(),
      refreshPerformanceData(false)
    ])

    // 检查结果并给出相应提示
    const storeSuccess = storeResult.status === 'fulfilled'
    const localSuccess = localResult.status === 'fulfilled'

    if (storeSuccess && localSuccess) {
      ElMessage.success('所有数据刷新成功')
    } else if (storeSuccess || localSuccess) {
      ElMessage.warning('部分数据刷新成功')
    } else {
      ElMessage.error('数据刷新失败，请检查网络连接')
    }
  } catch (error) {
    console.error('刷新数据时发生错误:', error)
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const refreshFrontendMetrics = () => {
  try {
    const newMetrics = store.getFrontendMetrics()
    if (newMetrics) {
      store.frontendMetrics = newMetrics
      ElMessage.success('前端性能数据已刷新')
    } else {
      ElMessage.warning('前端性能数据获取失败，请稍后重试')
    }
  } catch (error) {
    console.error('刷新前端性能数据失败:', error)
    ElMessage.error('前端性能数据刷新失败')
  }
}

const getHealthStatusType = () => {
  if (!store.systemHealth) return 'info'
  switch (store.systemHealth.overall) {
    case 'healthy':
      return 'success'
    case 'warning':
      return 'warning'
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
}

const getHealthStatusIcon = () => {
  if (!store.systemHealth) return 'CircleCheck'
  switch (store.systemHealth.overall) {
    case 'healthy':
      return 'CircleCheck'
    case 'warning':
      return 'Warning'
    case 'error':
      return 'CircleClose'
    default:
      return 'CircleCheck'
  }
}

const formatUptime = (seconds: number) => {
  const days = Math.floor(seconds / 86400)
  const hours = Math.floor((seconds % 86400) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)

  if (days > 0) {
    return `${days}天 ${hours}小时`
  } else if (hours > 0) {
    return `${hours}小时 ${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const getProgressValue = (metric: MetricCard) => {
  if (metric.unit === '%') {
    return Math.min(metric.value as number, 100)
  } else if (metric.unit === 'ms') {
    // 网络延迟，将ms转换为百分比（假设500ms为100%）
    return Math.min((metric.value as number) / 5, 100)
  }
  return 0
}

const getProgressColor = (metric: MetricCard) => {
  switch (metric.status) {
    case 'excellent':
      return '#67c23a'
    case 'normal':
      return '#409eff'
    case 'warning':
      return '#e6a23c'
    case 'error':
      return '#f56c6c'
    default:
      return '#909399'
  }
}

// 钻取分析功能
const focusedMetric = ref<string | null>(null)

const handleDrillDown = () => {
  const focus = route.query.focus as string
  const metric = route.query.metric as string

  if (focus && metric) {
    focusedMetric.value = focus

    // 显示相关的详细信息
    ElMessage.info(`正在查看 ${metric} 的详细信息`)

    // 根据指标类型展开相应的详细视图
    switch (focus) {
      case 'cpu':
        showCpuDetails.value = true
        break
      case 'memory':
        // 可以添加内存详细视图
        break
      case 'disk':
        // 可以添加磁盘详细视图
        break
      case 'network':
        // 可以添加网络详细视图
        break
    }

    // 滚动到相应的图表区域
    setTimeout(() => {
      const chartElement = document.querySelector('.performance-charts')
      if (chartElement) {
        chartElement.scrollIntoView({ behavior: 'smooth' })
      }
    }, 500)
  }
}

// 监听路由变化
watch(() => route.query, () => {
  handleDrillDown()
}, { immediate: true })

// 监听Store数据变化，自动更新图表
watch(() => store.metrics, async (newMetrics) => {
  if (newMetrics) {
    try {
      // 更新性能指标
      const adaptedMetrics = ChartDataAdapter.adaptPerformanceMetrics(newMetrics)
      adaptedMetrics.forEach((metric, index) => {
        if (performanceMetrics[index]) {
          performanceMetrics[index].value = metric.value
          performanceMetrics[index].change = metric.change
        }
      })

      // 更新内存图表
      const memoryChartAdapted = ChartDataAdapter.adaptMemoryDataForDoughnutChart(newMetrics)
      memoryChartData.value = memoryChartAdapted.data
      memoryDetails.value = memoryChartAdapted.details

      // 更新系统负载
      systemLoads.value = ChartDataAdapter.adaptSystemLoadData(newMetrics)

      // 获取历史数据更新图表
      const historicalData = await performanceService.getHistoricalData('6h')

      // 更新CPU图表
      const cpuChartAdapted = ChartDataAdapter.adaptCpuDataForLineChart(historicalData)
      cpuChartLabels.value = cpuChartAdapted.labels
      cpuChartData.value = cpuChartAdapted.data

      // 更新网络图表
      const networkChartAdapted = ChartDataAdapter.adaptNetworkDataForAreaChart(historicalData)
      networkChartData.value = networkChartAdapted

    } catch (error) {
      console.warn('自动更新图表数据失败:', error)
    }
  }
}, { deep: true })

// 监听Store告警数据变化
watch(() => store.alerts, (newAlerts) => {
  if (newAlerts && newAlerts.length > 0) {
    performanceAlerts.value = newAlerts.map(alert => ({
      id: alert.id,
      title: alert.title,
      description: alert.description,
      type: alert.type,
      timestamp: new Date(alert.timestamp),
      metric: alert.metric || `${alert.title}: 未知`
    }))
  }
}, { deep: true })

// 生命周期
onMounted(async () => {
  console.log('=== 组件开始挂载 ===')
  console.log('初始systemLoads数据:', systemLoads.value)

  try {
    // 初始化Store（包含实时监控）
    await store.initialize()
    console.log('Store初始化完成')

    // 加载初始数据
    await loadRealPerformanceData()
    console.log('真实数据加载完成')

    // 强制更新图表数据，确保显示
    await updateChartData()
    console.log('图表数据更新完成')

    // 加载历史数据
    await loadHistoryData(false)
    console.log('历史数据加载完成')

    // 处理钻取参数
    handleDrillDown()

    console.log('性能监控控制台初始化完成')
    console.log('最终数据状态:', {
      systemLoads: systemLoads.value,
      networkChartData: networkChartData.value,
      storeMetrics: store.metrics
    })

    // 确保系统负载数据可见
    if (systemLoads.value.length === 0) {
      console.warn('⚠️ 系统负载数据为空，保持0值')
      systemLoads.value = [
        { label: '1分钟', value: 0 },
        { label: '5分钟', value: 0 },
        { label: '15分钟', value: 0 }
      ]
    }
  } catch (error) {
    console.error('Performance monitoring initialization failed:', error)
    // 降级处理：尝试更新图表数据
    try {
      await updateChartData()
      ElMessage.warning('性能监控初始化部分失败，显示可用数据')
    } catch (fallbackError) {
      console.error('降级处理也失败:', fallbackError)
      ElMessage.error('性能监控初始化失败')
    }
  }
})

onUnmounted(() => {
  store.$dispose()
})

// 导出函数供其他组件使用
defineExpose({
  refreshPerformanceData
})
</script>

<style scoped lang="scss">
@import '@/styles/design-system.scss';

.performance-monitoring {
  padding: 24px;

  /* 控制面板 */
  .control-panel {
    margin-bottom: 32px;
    border-radius: 12px;
    border: 1px solid #E4E7ED;
    box-shadow: var(--shadow-sm);

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
        }

        .data-status {
          .el-tag {
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }
      }

      .panel-actions {
        display: flex;
        align-items: center;
        gap: 16px;

        .el-button {
          border-radius: var(--radius-lg);
        }
      }
    }

    /* 系统健康状态概览 */
    .health-overview {
      margin-top: var(--spacing-4);
      padding: var(--spacing-4);
      background: var(--bg-secondary);
      border-radius: var(--radius-lg);

      .health-summary {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-2);

        .uptime {
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
        }
      }

      .health-components {
        display: flex;
        gap: var(--spacing-3);
        flex-wrap: wrap;

        .health-component {
          display: flex;
          align-items: center;
          gap: var(--spacing-2);
          padding: var(--spacing-2) var(--spacing-3);
          background: white;
          border-radius: var(--radius-md);
          border: 1px solid var(--border-light);

          &.healthy {
            border-color: var(--color-success);
          }

          &.warning {
            border-color: var(--color-warning);
          }

          &.error {
            border-color: var(--color-danger);
          }

          .component-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
          }

          .component-info {
            .component-name {
              font-size: var(--font-size-sm);
              font-weight: var(--font-weight-medium);
              color: var(--text-primary);
            }

            .component-status {
              font-size: var(--font-size-xs);
              color: var(--text-secondary);
            }
          }
        }
      }
    }
  }

  /* 快速统计卡片 */
  .quick-stats-card {
    .quick-stats {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-3);

      .stat-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-3);

        .stat-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }

        .stat-content {
          .stat-value {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
          }

          .stat-label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
          }
        }
      }
    }
  }

  /* 前端性能卡片 */
  .frontend-performance-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .frontend-metrics {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-3);

      .metric-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-2) 0;
        border-bottom: 1px solid var(--border-light);

        &:last-child {
          border-bottom: none;
        }

        .metric-label {
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
        }

        .metric-value {
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
        }
      }
    }

    .monitoring-config {
      .el-form {
        .el-form-item {
          margin-bottom: var(--spacing-4);
          margin-right: var(--spacing-6);

          .el-form-item__label {
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
          }

          .el-select,
          .el-input-number {
            border-radius: var(--radius-lg);
          }
        }
      }
    }
  }

  /* 性能指标 */
  .performance-metrics {
    margin-bottom: var(--spacing-6);

    .metric-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
      }

      &.excellent {
        border-left: 4px solid var(--success-color);
      }

      &.normal {
        border-left: 4px solid var(--primary-color);
      }

      &.warning {
        border-left: 4px solid var(--warning-color);
      }

      &.error {
        border-left: 4px solid var(--error-color);
      }

      .metric-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-5);

        .metric-icon {
          width: 48px;
          height: 48px;
          border-radius: var(--radius-lg);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-lg);
        }

        .metric-info {
          flex: 1;

          .metric-value {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .metric-label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-2);
          }

          .metric-change {
            display: flex;
            align-items: center;
            gap: var(--spacing-1);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);

            &.positive {
              color: var(--success-color);
            }

            &.negative {
              color: var(--error-color);
            }
          }
        }
      }

      .metric-progress {
        padding: 0 var(--spacing-5) var(--spacing-3);

        .el-progress {
          .el-progress-bar__outer {
            background-color: var(--bg-light);
          }
        }
      }
    }
  }

  /* 性能图表 */
  .performance-charts {
    margin-bottom: var(--spacing-6);

    .chart-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      margin-bottom: var(--spacing-6);

      .el-card__header {
        border-bottom: 1px solid var(--border-light);

        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          span {
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
          }
        }
      }

      .chart-container {
        padding: 24px;
        min-height: 300px;

        .memory-details {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: var(--spacing-3);
          margin-top: var(--spacing-4);

          .memory-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);

            .memory-color {
              width: 12px;
              height: 12px;
              border-radius: 50%;
            }

            .memory-info {
              .memory-label {
                font-size: var(--font-size-xs);
                color: var(--text-secondary);
              }

              .memory-value {
                font-size: var(--font-size-sm);
                font-weight: var(--font-weight-medium);
                color: var(--text-primary);
              }
            }
          }
        }

        .load-gauges {
          display: flex;
          justify-content: space-around;
          align-items: center;
          padding: 24px;
          min-height: 200px;

          .load-gauge {
            text-align: center;
            flex: 1;
            max-width: 120px;

            .gauge-container {
              margin-bottom: 8px;
              position: relative;

              .gauge-text {
                font-size: 13px;
                font-weight: bold;
                color: #303133;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 10;
              }
            }

            .gauge-label {
              font-size: 12px;
              color: #909399;
              font-weight: 500;
              margin-top: 8px;
            }
          }
        }
      }
    }
  }

  /* 警告卡片 */
  .alerts-card {
    margin-bottom: var(--spacing-6);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);

    .el-card__header {
      border-bottom: 1px solid var(--border-light);

      .alerts-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        span {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-semibold);
          color: var(--text-primary);
        }
      }
    }

    .alerts-list {
      .alert-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: var(--spacing-2);

        .alert-time {
          font-size: var(--font-size-xs);
          color: var(--text-secondary);
        }

        .alert-metric {
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
        }
      }
    }
  }

  /* 历史记录卡片 */
  .history-card {
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);

    .el-card__header {
      border-bottom: 1px solid var(--border-light);

      .history-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        span {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-semibold);
          color: var(--text-primary);
        }

        .history-controls {
          display: flex;
          align-items: center;
          gap: var(--spacing-3);
        }
      }
    }

    .history-pagination {
      padding: var(--spacing-4);
      border-top: 1px solid var(--border-light);
      display: flex;
      justify-content: center;
    }
  }
}

/* 响应式设计 */
@include respond-to('lg') {
  .performance-monitoring {
    .control-panel {
      .panel-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-3);
      }

      .monitoring-config .el-form .el-form-item {
        margin-right: 0;
        margin-bottom: var(--spacing-4);
      }
    }

    .performance-metrics .el-col {
      margin-bottom: var(--spacing-4);
    }

    .performance-charts .chart-card .chart-container .memory-details {
      grid-template-columns: 1fr;
    }
  }
}

@include respond-to('md') {
  .performance-monitoring {
    padding: var(--spacing-3);

    .performance-charts .chart-card .chart-container .load-gauges {
      flex-direction: column;
      gap: 24px;

      .load-gauge .gauge-container {
        .el-progress {
          width: 60px !important;
          height: 60px !important;
        }
      }
    }

    .alerts-card .alerts-list .alert-content {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-1);
    }

    .history-card .el-card__header .history-header {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-3);

      .history-controls {
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}

@include respond-to('sm') {
  .performance-monitoring {
    .control-panel {
      .monitoring-config .el-form {
        .el-form-item {
          width: 100%;

          .el-select,
          .el-input-number {
            width: 100%;
          }
        }
      }
    }

    .performance-charts .chart-card .chart-container .memory-details .memory-item {
      .memory-info {
        .memory-label,
        .memory-value {
          font-size: var(--font-size-xs);
        }
      }
    }

    .history-card {
      .el-table {
        font-size: var(--font-size-xs);
      }

      .history-pagination {
        .el-pagination {
          .el-pagination__sizes,
          .el-pagination__jump {
            display: none;
          }
        }
      }
    }
  }
}
</style>
