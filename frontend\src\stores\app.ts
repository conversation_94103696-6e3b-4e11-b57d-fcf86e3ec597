/**
 * 应用状态管理
 * 管理应用全局状态、主题、语言等设置
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 主题类型
export type Theme = 'light' | 'dark' | 'auto'



// 侧边栏状态
export interface SidebarState {
  collapsed: boolean
  width: number
  collapsedWidth: number
}

// 应用设置
export interface AppSettings {
  theme: Theme
  showBreadcrumb: boolean
  showTabs: boolean
  fixedHeader: boolean
  sidebarLogo: boolean
  showFooter: boolean
  colorWeak: boolean
  grayMode: boolean
}

export const useAppStore = defineStore('app', () => {
  // 状态
  const sidebar = ref<SidebarState>({
    collapsed: false,
    width: 240,
    collapsedWidth: 64
  })

  const settings = ref<AppSettings>({
    theme: 'light',
    showBreadcrumb: true,
    showTabs: true,
    fixedHeader: true,
    sidebarLogo: true,
    showFooter: true,
    colorWeak: false,
    grayMode: false
  })

  const loading = ref(false)
  const device = ref<'desktop' | 'tablet' | 'mobile'>('desktop')
  const pageTitle = ref('')
  const breadcrumbs = ref<Array<{ name: string; path?: string }>>([])

  // 从localStorage加载设置
  const loadSettings = () => {
    const savedSettings = localStorage.getItem('app-settings')
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings)
        settings.value = { ...settings.value, ...parsed }
      } catch (error) {
        console.error('Failed to parse saved settings:', error)
      }
    }

    const savedSidebar = localStorage.getItem('sidebar-state')
    if (savedSidebar) {
      try {
        const parsed = JSON.parse(savedSidebar)
        sidebar.value = { ...sidebar.value, ...parsed }
      } catch (error) {
        console.error('Failed to parse saved sidebar state:', error)
      }
    }
  }

  // 保存设置到localStorage
  const saveSettings = () => {
    localStorage.setItem('app-settings', JSON.stringify(settings.value))
    localStorage.setItem('sidebar-state', JSON.stringify(sidebar.value))
  }

  // 计算属性
  const isMobile = computed(() => device.value === 'mobile')
  const isTablet = computed(() => device.value === 'tablet')
  const isDesktop = computed(() => device.value === 'desktop')

  const currentSidebarWidth = computed(() => 
    sidebar.value.collapsed ? sidebar.value.collapsedWidth : sidebar.value.width
  )

  const isDarkTheme = computed(() => {
    if (settings.value.theme === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    return settings.value.theme === 'dark'
  })

  // 操作方法
  const toggleSidebar = () => {
    sidebar.value.collapsed = !sidebar.value.collapsed
    saveSettings()
  }

  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebar.value.collapsed = collapsed
    saveSettings()
  }

  const setSidebarWidth = (width: number) => {
    sidebar.value.width = width
    saveSettings()
  }

  const setTheme = (theme: Theme) => {
    settings.value.theme = theme
    applyTheme()
    saveSettings()
  }



  const updateSettings = (newSettings: Partial<AppSettings>) => {
    settings.value = { ...settings.value, ...newSettings }
    applyTheme()
    saveSettings()
  }

  const setDevice = (deviceType: 'desktop' | 'tablet' | 'mobile') => {
    device.value = deviceType
    
    // 移动端自动收起侧边栏
    if (deviceType === 'mobile') {
      sidebar.value.collapsed = true
    }
  }

  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  const setPageTitle = (title: string) => {
    pageTitle.value = title
    document.title = title ? `${title} - 智慧教育管理系统` : '智慧教育管理系统'
  }

  const setBreadcrumbs = (crumbs: Array<{ name: string; path?: string }>) => {
    breadcrumbs.value = crumbs
  }

  // 应用主题
  const applyTheme = () => {
    const html = document.documentElement
    
    // 移除现有主题类
    html.classList.remove('light', 'dark')
    
    // 应用新主题
    if (isDarkTheme.value) {
      html.classList.add('dark')
    } else {
      html.classList.add('light')
    }

    // 应用其他设置
    if (settings.value.colorWeak) {
      html.classList.add('color-weak')
    } else {
      html.classList.remove('color-weak')
    }

    if (settings.value.grayMode) {
      html.classList.add('gray-mode')
    } else {
      html.classList.remove('gray-mode')
    }
  }

  // 监听系统主题变化
  const watchSystemTheme = () => {
    if (settings.value.theme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      mediaQuery.addEventListener('change', applyTheme)
      
      return () => {
        mediaQuery.removeEventListener('change', applyTheme)
      }
    }
  }

  // 监听窗口大小变化
  const watchWindowSize = () => {
    const updateDevice = () => {
      const width = window.innerWidth
      if (width < 768) {
        setDevice('mobile')
      } else if (width < 1024) {
        setDevice('tablet')
      } else {
        setDevice('desktop')
      }
    }

    updateDevice()
    window.addEventListener('resize', updateDevice)
    
    return () => {
      window.removeEventListener('resize', updateDevice)
    }
  }

  // 重置设置
  const resetSettings = () => {
    settings.value = {
      theme: 'light',
      language: 'zh-CN',
      showBreadcrumb: true,
      showTabs: true,
      fixedHeader: true,
      sidebarLogo: true,
      showFooter: true,
      colorWeak: false,
      grayMode: false
    }
    
    sidebar.value = {
      collapsed: false,
      width: 240,
      collapsedWidth: 64
    }
    
    applyTheme()
    saveSettings()
  }

  // 初始化应用
  const initializeApp = () => {
    loadSettings()
    applyTheme()
    
    const cleanupSystemTheme = watchSystemTheme()
    const cleanupWindowSize = watchWindowSize()
    
    return () => {
      cleanupSystemTheme?.()
      cleanupWindowSize?.()
    }
  }

  return {
    // 状态
    sidebar,
    settings,
    loading,
    device,
    pageTitle,
    breadcrumbs,
    
    // 计算属性
    isMobile,
    isTablet,
    isDesktop,
    currentSidebarWidth,
    isDarkTheme,
    
    // 方法
    toggleSidebar,
    setSidebarCollapsed,
    setSidebarWidth,
    setTheme,
    updateSettings,
    setDevice,
    setLoading,
    setPageTitle,
    setBreadcrumbs,
    applyTheme,
    watchSystemTheme,
    watchWindowSize,
    resetSettings,
    initializeApp,
    loadSettings,
    saveSettings
  }
})

export default useAppStore
