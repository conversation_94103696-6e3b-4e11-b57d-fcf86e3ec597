#!/usr/bin/env python3
"""
测试网络延迟检测功能
"""

import subprocess
import platform
import time
import re

def get_network_latency(host="*******", timeout=3):
    """
    获取网络延迟
    """
    try:
        system = platform.system().lower()
        
        if system == "windows":
            # Windows系统使用ping命令
            cmd = ["ping", "-n", "1", "-w", str(timeout * 1000), host]
        else:
            # Unix系统使用ping命令
            cmd = ["ping", "-c", "1", "-W", str(timeout), host]
        
        start_time = time.time()
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=timeout + 1
        )
        end_time = time.time()
        
        print(f"命令: {' '.join(cmd)}")
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout}")
        print(f"错误: {result.stderr}")
        
        if result.returncode == 0:
            # 解析ping结果获取延迟时间
            output = result.stdout.lower()
            
            if system == "windows":
                # Windows ping输出格式: "时间=XXXms" 或 "time=XXXms"
                match = re.search(r'时间[=<](\d+)ms|time[=<](\d+)ms', output)
                if match:
                    latency = int(match.group(1) or match.group(2))
                    return latency
                # 尝试匹配中文格式
                match = re.search(r'时间=(\d+)ms', output)
                if match:
                    return int(match.group(1))
            else:
                # Unix ping输出格式: "time=XXX ms"
                match = re.search(r'time=(\d+\.?\d*)', output)
                if match:
                    latency = float(match.group(1))
                    return int(latency)
            
            # 如果无法解析，使用总时间作为估算
            total_time = (end_time - start_time) * 1000
            return int(total_time)
        else:
            return None
            
    except subprocess.TimeoutExpired:
        print(f"网络延迟检测超时: {host}")
        return None
    except Exception as e:
        print(f"网络延迟检测失败: {e}")
        return None

if __name__ == "__main__":
    print("测试网络延迟检测功能")
    print("=" * 50)
    
    hosts = ["*******", "*******", "***************"]
    
    for host in hosts:
        print(f"\n测试主机: {host}")
        latency = get_network_latency(host)
        if latency is not None:
            print(f"延迟: {latency}ms")
        else:
            print("检测失败")
        print("-" * 30)
