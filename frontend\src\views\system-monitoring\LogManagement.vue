<template>
  <div class="log-management">
    <!-- 状态提示栏 -->
    <el-alert
      v-if="!authStore.isAuthenticated"
      title="演示模式"
      type="warning"
      :closable="false"
      show-icon
      style="margin-bottom: 16px;"
    >
      <template #default>
        <div>
          <p>当前未登录，正在使用演示模式显示模拟数据。</p>
          <p>
            <el-button type="primary" size="small" @click="goToLogin">立即登录</el-button>
            以查看真实的系统日志数据。
          </p>
        </div>
      </template>
    </el-alert>



    <!-- 日志统计概览 -->
    <div class="log-statistics">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :lg="6" v-for="(stat, index) in logStats" :key="index">
          <el-card :class="['stat-card', stat.type]">
            <div class="stat-content">
              <div class="stat-icon" :style="{ backgroundColor: stat.color }">
                <el-icon><component :is="stat.icon" /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-trend" :class="stat.trend >= 0 ? 'positive' : 'negative'">
                  <el-icon><ArrowUp v-if="stat.trend >= 0" /><ArrowDown v-else /></el-icon>
                  {{ Math.abs(stat.trend) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 日志配置面板 -->
    <el-card class="log-config-panel">
      <template #header>
        <div class="config-header">
          <span>日志配置管理</span>
          <el-button type="primary" size="small" @click="showConfigDialog = true">
            <el-icon><Setting /></el-icon>
            配置设置
          </el-button>
        </div>
      </template>

      <el-row :gutter="24">
        <el-col :span="8">
          <div class="config-item">
            <div class="config-label">当前日志级别</div>
            <div class="config-value">{{ logConfig.logLevel }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="config-item">
            <div class="config-label">日志格式</div>
            <div class="config-value">{{ logConfig.logFormat === 'standard' ? '标准格式' : logConfig.logFormat === 'json' ? 'JSON格式' : '详细格式' }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="config-item">
            <div class="config-label">保留天数</div>
            <div class="config-value">{{ logConfig.retentionDays }} 天</div>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="24" style="margin-top: 16px;">
        <el-col :span="8">
          <div class="config-item">
            <div class="config-label">文件大小限制</div>
            <div class="config-value">{{ logConfig.maxFileSize }} MB</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="config-item">
            <div class="config-label">日志轮转</div>
            <div class="config-value">
              <el-tag :type="logConfig.enableRotation ? 'success' : 'info'">
                {{ logConfig.enableRotation ? '已启用' : '已禁用' }}
              </el-tag>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="config-item">
            <div class="config-label">启用的日志类型</div>
            <div class="config-value">{{ logConfig.enabledLogTypes.length }} 种</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 实时日志流 -->
    <el-card class="log-stream">
      <template #header>
        <div class="stream-header">
          <div class="stream-title">
            <h3>日志管理控制台</h3>
            <div class="stream-status">
              <el-tag
                :type="getConnectionStatusType()"
                size="small"
                class="connection-status"
              >
                {{ getConnectionStatusText() }}
              </el-tag>
              <el-tag
                type="info"
                size="small"
                class="log-count"
              >
                {{ logCount }}/100 条
              </el-tag>
            </div>
          </div>
          
          <!-- 日志筛选器 -->
          <div class="log-filters">
            <el-form :model="filters" inline>
              <el-form-item label="日志级别">
                <el-select v-model="filters.level" placeholder="选择级别" @change="handleFilterChange">
                  <el-option label="全部级别" value="" />
                  <el-option label="DEBUG" value="DEBUG" />
                  <el-option label="INFO" value="INFO" />
                  <el-option label="WARNING" value="WARNING" />
                  <el-option label="ERROR" value="ERROR" />
                  <el-option label="CRITICAL" value="CRITICAL" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="时间范围">
                <el-date-picker
                  v-model="filters.dateRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  @change="handleFilterChange"
                />
              </el-form-item>
              
              <el-form-item label="关键词">
                <el-input
                  v-model="filters.keyword"
                  placeholder="搜索日志内容"
                  @input="handleSearchInput"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
              
              <el-form-item label="模块">
                <div style="display: flex; align-items: center; gap: 8px;">
                  <el-select v-model="filters.module" placeholder="选择模块" @change="handleFilterChange">
                    <el-option label="全部模块" value="" />
                    <el-option
                      v-for="module in logModules"
                      :key="module"
                      :label="module"
                      :value="module"
                    />
                    <template #empty>
                      <div style="padding: 10px; text-align: center; color: #999;">
                        <div v-if="logModules.length === 0">使用默认模块列表</div>
                        <div v-else>没有匹配的模块</div>
                      </div>
                    </template>
                  </el-select>
                  <el-tooltip content="模块列表状态" placement="top">
                    <el-tag
                      :type="logModules.length > 0 ? 'success' : 'warning'"
                      size="small"
                    >
                      {{ logModules.length > 0 ? `${logModules.length}个模块` : '使用默认' }}
                    </el-tag>
                  </el-tooltip>
                </div>
              </el-form-item>
            </el-form>
          </div>
          
          <div class="stream-controls">
            <el-button type="primary" @click="refreshLogs" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新日志
            </el-button>
            <el-dropdown>
              <el-button>
                重试操作
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="retryLoadSystemLogs">重新加载日志</el-dropdown-item>
                  <el-dropdown-item @click="retryLoadLogModules">重新加载模块</el-dropdown-item>
                <el-dropdown-item @click="enableFallbackMode" divided>启用演示模式</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-button @click="clearAllLogs" :loading="clearing">
              <el-icon><Delete /></el-icon>
              清理日志
            </el-button>
            <el-button @click="downloadLogs" :loading="downloading">
              <el-icon><Download /></el-icon>
              下载日志
            </el-button>
            <el-switch
              v-model="realTimeEnabled"
              @change="toggleRealTime"
              active-text="实时更新"
              inactive-text="暂停更新"
            />
            <el-button size="small" @click="clearLogStream">
              <el-icon><Delete /></el-icon>
              清空日志
            </el-button>
          </div>
        </div>
      </template>

      <div class="log-stream-container" ref="logStreamRef">
        <div
          v-for="(log, index) in filteredLogs"
          :key="index"
          :class="['log-entry', `level-${log.level.toLowerCase()}`]"
          @click="showLogDetails(log)"
        >
          <div class="log-timestamp">{{ formatTimestamp(log.created_at) }}</div>
          <div class="log-level" :class="`level-${log.level.toLowerCase()}`">
            {{ log.level }}
          </div>
          <div class="log-module">{{ log.module }}</div>
          <div class="log-message">{{ log.message }}</div>
          <div class="log-actions">
            <el-button size="small" text @click.stop="copyLogEntry(log)">
              <el-icon><CopyDocument /></el-icon>
            </el-button>
            <el-button size="small" text @click.stop="showLogDetails(log)">
              <el-icon><View /></el-icon>
            </el-button>
          </div>
        </div>
        
        <div v-if="filteredLogs.length === 0" class="empty-logs">
          <div class="empty-icon">📝</div>
          <div class="empty-text">
            <p>暂无日志数据</p>
            <p class="empty-hint">
              {{ realTimeEnabled ? '等待新日志产生...' : '点击上方开关启用实时更新' }}
            </p>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="log-pagination">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="totalLogs"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 日志详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="日志详情"
      width="70%"
      :before-close="handleDetailClose"
    >
      <div v-if="selectedLog" class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="时间戳">
            {{ formatTimestamp(selectedLog.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="日志级别">
            <el-tag :type="getLevelTagType(selectedLog.level)">
              {{ selectedLog.level }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="模块">
            {{ selectedLog.module }}
          </el-descriptions-item>
          <el-descriptions-item label="用户">
            {{ selectedLog.username || '系统' }}
          </el-descriptions-item>
          <el-descriptions-item label="IP地址">
            {{ selectedLog.ip_address || 'N/A' }}
          </el-descriptions-item>
          <el-descriptions-item label="操作">
            {{ selectedLog.action || 'N/A' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="log-message-detail">
          <h4>详细信息</h4>
          <el-input
            v-model="selectedLog.message"
            type="textarea"
            :rows="6"
            readonly
            class="log-message-textarea"
          />
        </div>
        
        <div v-if="selectedLog.details" class="log-details">
          <h4>详细信息</h4>
          <el-input
            v-model="selectedLog.details"
            type="textarea"
            :rows="8"
            readonly
            class="details-textarea"
          />
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="copyLogDetails">复制详情</el-button>
          <el-button type="primary" @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 日志配置对话框 -->
    <el-dialog
      v-model="showConfigDialog"
      title="日志配置设置"
      width="800px"
      :before-close="handleConfigClose"
    >
      <el-form :model="logConfig" ref="logConfigFormRef" label-width="120px">
        <el-divider content-position="left">基础配置</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="日志级别" prop="logLevel">
              <el-select v-model="logConfig.logLevel" placeholder="请选择日志级别">
                <el-option label="DEBUG" value="DEBUG" />
                <el-option label="INFO" value="INFO" />
                <el-option label="WARNING" value="WARNING" />
                <el-option label="ERROR" value="ERROR" />
                <el-option label="CRITICAL" value="CRITICAL" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="日志格式" prop="logFormat">
              <el-select v-model="logConfig.logFormat" placeholder="请选择日志格式">
                <el-option label="标准格式" value="standard" />
                <el-option label="JSON格式" value="json" />
                <el-option label="详细格式" value="detailed" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="日志文件路径" prop="logFilePath">
              <el-input v-model="logConfig.logFilePath" placeholder="/var/log/wiscude/" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单文件最大大小" prop="maxFileSize">
              <el-input-number v-model="logConfig.maxFileSize" :min="1" :max="1000" />
              <span style="margin-left: 8px;">MB</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="日志保留天数" prop="retentionDays">
              <el-input-number v-model="logConfig.retentionDays" :min="1" :max="365" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="启用日志轮转">
              <el-switch v-model="logConfig.enableRotation" />
              <span style="margin-left: 8px; color: #999;">自动轮转日志文件</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="日志类型配置">
          <el-checkbox-group v-model="logConfig.enabledLogTypes">
            <el-checkbox label="system">系统日志</el-checkbox>
            <el-checkbox label="access">访问日志</el-checkbox>
            <el-checkbox label="error">错误日志</el-checkbox>
            <el-checkbox label="security">安全日志</el-checkbox>
            <el-checkbox label="audit">审计日志</el-checkbox>
            <el-checkbox label="performance">性能日志</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-divider content-position="left">文件管理</el-divider>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="日志类型">
              <el-select v-model="logConfig.viewLogType" placeholder="选择日志类型" @change="loadLogFiles">
                <el-option label="系统日志" value="system" />
                <el-option label="访问日志" value="access" />
                <el-option label="错误日志" value="error" />
                <el-option label="安全日志" value="security" />
                <el-option label="审计日志" value="audit" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="日志文件">
              <el-select v-model="logConfig.selectedLogFile" placeholder="选择日志文件" @change="loadLogContent">
                <el-option
                  v-for="file in logConfig.logFiles"
                  :key="file.name"
                  :label="`${file.name} (${file.size})`"
                  :value="file.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作">
              <el-button type="primary" @click="downloadLogFile" :disabled="!logConfig.selectedLogFile" size="small">
                下载日志
              </el-button>
              <el-button type="warning" @click="clearLogFile" :disabled="!logConfig.selectedLogFile" size="small">
                清空日志
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetLogConfig">重置</el-button>
          <el-button @click="showConfigDialog = false">取消</el-button>
          <el-button type="primary" @click="saveLogConfig" :loading="configSaving">保存配置</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Delete, Download, Search, ArrowUp, ArrowDown,
  CopyDocument, View, Document, Warning, CircleClose, InfoFilled, Setting
} from '@element-plus/icons-vue'
import { systemLogsApi, LogWebSocketManager, type SystemLog, type LogQueryParams } from '@/api/system-logs'
import { useAuthStore } from '@/store/auth'

// 使用auth store
const authStore = useAuthStore()

// 检查用户认证状态
const checkAuthStatus = async () => {
  // 如果已经认证，直接返回true
  if (authStore.isAuthenticated && authStore.accessToken) {
    return true
  }

  // 尝试检查认证状态（可能从localStorage恢复）
  try {
    const isAuth = await authStore.checkAuth()
    if (isAuth) {
      return true
    }
  } catch (error) {
    console.error('认证检查失败:', error)
  }

  ElMessage.warning('请先登录以访问日志管理功能')
  return false
}

// 模拟日志生成器（降级方案）
const generateMockLogs = (count: number = 10): SystemLog[] => {
  const levels = ['INFO', 'WARNING', 'ERROR', 'DEBUG']
  const modules = ['SYSTEM', 'AUTH', 'USER', 'API', 'DATABASE', 'CACHE', 'EMAIL', 'FILE', 'SYNC', 'ADMIN']
  const actions = ['用户登录', '数据同步', '缓存更新', '邮件发送', '数据库查询', '文件上传', '系统检查', '配置更新']
  const messages = [
    '操作执行成功',
    '数据处理完成',
    '系统状态正常',
    '用户认证通过',
    '缓存刷新完成',
    '邮件发送成功',
    '文件处理完成',
    '配置更新生效'
  ]

  const mockLogs: SystemLog[] = []
  const now = new Date()

  for (let i = 0; i < count; i++) {
    const logTime = new Date(now.getTime() - i * 30000) // 每30秒一条日志
    mockLogs.push({
      id: `mock-${Date.now()}-${i}`,
      user_id: 'mock-user',
      username: 'demo_user',
      action: actions[Math.floor(Math.random() * actions.length)],
      module: modules[Math.floor(Math.random() * modules.length)],
      level: levels[Math.floor(Math.random() * levels.length)],
      message: messages[Math.floor(Math.random() * messages.length)],
      details: `模拟日志详情 - ${i + 1}`,
      ip_address: '127.0.0.1',
      created_at: logTime.toISOString(),
      updated_at: logTime.toISOString()
    })
  }

  return mockLogs
}

// 启用降级模式
const enableFallbackMode = () => {
  ElMessage.info('已启用演示模式，显示模拟日志数据')
  logs.value = generateMockLogs(50)
  totalLogs.value = 50

  // 如果实时更新开启，启动模拟日志生成
  if (realTimeEnabled.value) {
    startMockRealTimeUpdates()
  }
}

// 跳转到登录页面
const goToLogin = () => {
  ElMessageBox.confirm(
    '将跳转到登录页面，当前页面数据将丢失。确定继续吗？',
    '确认跳转',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 跳转到登录页面
    window.location.href = '/login'
  }).catch(() => {
    // 用户取消，不做任何操作
  })
}

// 模拟实时日志更新
let mockTimer: number | null = null

const startMockRealTimeUpdates = () => {
  if (mockTimer) return

  connectionStatus.value = 'connected'

  mockTimer = window.setInterval(() => {
    if (realTimeEnabled.value) {
      const newLog = generateMockLogs(1)[0]
      logs.value.unshift(newLog)

      // 限制日志数量为100条
      if (logs.value.length > 100) {
        logs.value = logs.value.slice(0, 100)
      }

      // 自动滚动到顶部
      nextTick(() => {
        if (logStreamRef.value) {
          logStreamRef.value.scrollTop = 0
        }
      })
    }
  }, 5000) // 每5秒生成一条新日志
}

const stopMockRealTimeUpdates = () => {
  if (mockTimer) {
    clearInterval(mockTimer)
    mockTimer = null
  }
}

// 响应式数据
const loading = ref(false)
const clearing = ref(false)
const downloading = ref(false)
const realTimeEnabled = ref(true)
const detailDialogVisible = ref(false)
const selectedLog = ref<SystemLog | null>(null)
const logStreamRef = ref<HTMLElement>()

// 日志数据
const logs = ref<SystemLog[]>([])
const logModules = ref<string[]>([])

// WebSocket管理器
let wsManager: LogWebSocketManager | null = null

// 定时轮询管理
let pollingTimer: number | null = null
const pollingInterval = 4000 // 4秒轮询间隔
const useWebSocket = ref(true) // 是否使用WebSocket，否则使用轮询
const connectionStatus = ref<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected')

// 重连机制
let reconnectTimer: number | null = null
const maxReconnectAttempts = 3
const reconnectAttempts = ref(0)
const reconnectDelay = 5000 // 5秒后重连

// 筛选器
const filters = reactive({
  level: '',
  dateRange: [] as string[],
  keyword: '',
  module: ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 50
})

const totalLogs = ref(0)

// 日志配置相关
const showConfigDialog = ref(false)
const configSaving = ref(false)
const logConfigFormRef = ref()

const logConfig = reactive({
  logLevel: 'INFO',
  logFormat: 'standard',
  logFilePath: '/var/log/wiscude/',
  maxFileSize: 100,
  retentionDays: 30,
  enableRotation: true,
  enabledLogTypes: ['system', 'access', 'error', 'security'],
  viewLogType: '',
  selectedLogFile: '',
  logFiles: [] as Array<{name: string, size: string}>,
  logContent: ''
})

// 日志统计数据
const logStats = reactive([
  {
    label: '今日日志',
    value: '12,456',
    trend: 8.5,
    color: '#3b82f6',
    icon: 'Document',
    type: 'info'
  },
  {
    label: '错误日志',
    value: '23',
    trend: -12.3,
    color: '#ef4444',
    icon: 'CircleClose',
    type: 'error'
  },
  {
    label: '警告日志',
    value: '156',
    trend: 5.2,
    color: '#f59e0b',
    icon: 'Warning',
    type: 'warning'
  },
  {
    label: '信息日志',
    value: '11,277',
    trend: 15.7,
    color: '#10b981',
    icon: 'InfoFilled',
    type: 'success'
  }
])

// 重试加载日志模块
const retryLoadLogModules = async () => {
  await loadLogModules()
  ElMessage.success('日志模块列表已重新加载')
}

// 重试加载系统日志
const retryLoadSystemLogs = async () => {
  await loadSystemLogs()
  ElMessage.success('系统日志已重新加载')
}

// 真实数据加载函数
const loadSystemLogs = async () => {
  // 检查认证状态
  if (!(await checkAuthStatus())) {
    logs.value = []
    totalLogs.value = 0
    return
  }

  try {
    loading.value = true

    const params: LogQueryParams = {
      page: pagination.currentPage,
      page_size: pagination.pageSize,
      level: filters.level || undefined,
      module: filters.module || undefined,
      keyword: filters.keyword || undefined
    }

    // 处理日期范围
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.start_date = filters.dateRange[0]
      params.end_date = filters.dateRange[1]
    }

    const response = await systemLogsApi.getSystemLogs(params)
    logs.value = response.items
    totalLogs.value = response.total

  } catch (error: any) {
    console.error('加载系统日志失败:', error)

    // 根据错误类型提供不同的提示和处理
    if (error?.response?.status === 401) {
      ElMessage({
        message: '需要登录才能查看真实日志，已启用演示模式',
        type: 'warning',
        duration: 5000,
        showClose: true
      })
      // 启用演示模式
      enableFallbackMode()
    } else if (error?.response?.status === 404) {
      ElMessage.info('系统日志接口暂不可用，已启用演示模式')
      enableFallbackMode()
    } else if (error?.response?.status === 500) {
      ElMessage.error('服务器内部错误，已启用演示模式')
      enableFallbackMode()
    } else {
      ElMessage.error('加载系统日志失败，已启用演示模式')
      enableFallbackMode()
    }
  } finally {
    loading.value = false
  }
}

// 加载日志模块列表
const loadLogModules = async () => {
  // 检查认证状态
  if (!(await checkAuthStatus())) {
    // 使用默认模块列表
    logModules.value = [
      'SYSTEM', 'AUTH', 'USER', 'API', 'DATABASE',
      'CACHE', 'EMAIL', 'FILE', 'SYNC', 'ADMIN'
    ]
    return
  }

  try {
    const response = await systemLogsApi.getLogModules()
    logModules.value = response.modules || []

    // 如果没有模块数据，提供默认模块列表
    if (logModules.value.length === 0) {
      logModules.value = [
        'SYSTEM', 'AUTH', 'USER', 'API', 'DATABASE',
        'CACHE', 'EMAIL', 'FILE', 'SYNC', 'ADMIN'
      ]
      console.warn('未获取到日志模块数据，使用默认模块列表')
    }
  } catch (error: any) {
    console.error('加载日志模块失败:', error)

    // 提供降级方案
    logModules.value = [
      'SYSTEM', 'AUTH', 'USER', 'API', 'DATABASE',
      'CACHE', 'EMAIL', 'FILE', 'SYNC', 'ADMIN'
    ]

    // 根据错误类型提供不同的提示
    if (error?.response?.status === 401) {
      ElMessage({
        message: '需要登录才能访问日志管理功能，已启用演示模式',
        type: 'warning',
        duration: 5000,
        showClose: true
      })
      console.log('认证失败，启用演示模式')
    } else if (error?.response?.status === 404) {
      ElMessage({
        message: '日志模块接口暂不可用，已使用默认模块列表',
        type: 'info',
        duration: 3000
      })
      console.log('接口404，使用默认模块列表')
    } else if (error?.response?.status === 500) {
      ElMessage.error('服务器内部错误，已使用默认模块列表')
      console.log('服务器500错误，使用默认模块列表')
    } else if (error?.code === 'NETWORK_ERROR' || !error?.response) {
      ElMessage.error('网络连接失败，已使用默认模块列表')
      console.log('网络错误，使用默认模块列表')
    } else {
      ElMessage.error(`加载日志模块失败 (${error?.response?.status || 'Unknown'}), 已使用默认模块列表`)
      console.log('其他错误，使用默认模块列表:', error)
    }
  }
}

// 由于过滤现在在服务器端进行，这里直接返回日志数据
// 确保日志按时间倒序排列（最新的在顶部）
const filteredLogs = computed(() => {
  return logs.value.sort((a, b) => {
    const timeA = new Date(a.created_at).getTime()
    const timeB = new Date(b.created_at).getTime()
    return timeB - timeA // 倒序排列
  })
})

// 日志条目计数
const logCount = computed(() => logs.value.length)

// 方法实现
const refreshLogs = async () => {
  await loadSystemLogs()
  ElMessage.success('日志刷新成功')
}

// 搜索日志
const searchLogs = async () => {
  pagination.currentPage = 1
  await loadSystemLogs()
}

// 重置筛选器
const resetFilters = async () => {
  filters.level = ''
  filters.module = ''
  filters.keyword = ''
  filters.dateRange = []
  pagination.currentPage = 1
  await loadSystemLogs()
}

// 分页变化处理
const handlePageChange = async (page: number) => {
  pagination.currentPage = page
  await loadSystemLogs()
}

const handlePageSizeChange = async (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  await loadSystemLogs()
}

// 清理日志
const clearAllLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清理所有日志吗？此操作不可恢复。',
      '确认清理',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    clearing.value = true
    await systemLogsApi.cleanupSystemLogs(0) // 清理所有日志
    await loadSystemLogs()
    ElMessage.success('日志清理成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清理日志失败:', error)
      ElMessage.error('清理日志失败')
    }
  } finally {
    clearing.value = false
  }
}

const downloadLogs = async () => {
  downloading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    // 模拟下载日志文件
    const blob = new Blob([JSON.stringify(logs.value, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `system-logs-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    ElMessage.success('日志下载成功')
  } catch (error) {
    ElMessage.error('日志下载失败')
  } finally {
    downloading.value = false
  }
}

const handleSearchInput = () => {
  // 防抖搜索
  clearTimeout(searchTimer)
  searchTimer = setTimeout(async () => {
    pagination.currentPage = 1
    await loadSystemLogs()
  }, 500)
}

const handleFilterChange = async () => {
  pagination.currentPage = 1
  await loadSystemLogs()
}

let searchTimer: number

// 连接状态相关方法
const getConnectionStatusType = () => {
  switch (connectionStatus.value) {
    case 'connected':
      return 'success'
    case 'connecting':
      return 'warning'
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
}

const getConnectionStatusText = () => {
  switch (connectionStatus.value) {
    case 'connected':
      return useWebSocket.value ? 'WebSocket已连接' : '轮询模式'
    case 'connecting':
      return '连接中...'
    case 'error':
      return reconnectAttempts.value > 0 ? `重连中(${reconnectAttempts.value}/${maxReconnectAttempts})` : '连接错误'
    default:
      return '未连接'
  }
}

// 重连机制
const scheduleReconnect = () => {
  if (reconnectAttempts.value >= maxReconnectAttempts) {
    ElMessage.error('连接失败次数过多，请手动重新启用实时更新')
    return
  }

  if (reconnectTimer) {
    clearTimeout(reconnectTimer)
  }

  reconnectAttempts.value++
  ElMessage.warning(`连接断开，${reconnectDelay / 1000}秒后尝试重连 (${reconnectAttempts.value}/${maxReconnectAttempts})`)

  reconnectTimer = window.setTimeout(() => {
    if (realTimeEnabled.value) {
      console.log(`尝试重连 (${reconnectAttempts.value}/${maxReconnectAttempts})`)
      startRealTimeUpdates()
    }
  }, reconnectDelay)
}

// 重置重连计数
const resetReconnectAttempts = () => {
  reconnectAttempts.value = 0
  if (reconnectTimer) {
    clearTimeout(reconnectTimer)
    reconnectTimer = null
  }
}

const toggleRealTime = async (enabled: string | number | boolean) => {
  const isEnabled = Boolean(enabled)
  if (isEnabled) {
    resetReconnectAttempts() // 重新启用时重置重连状态
    await startRealTimeUpdates()
    ElMessage.success('实时更新已开启')
  } else {
    stopRealTimeUpdates()
    resetReconnectAttempts() // 停用时也重置重连状态
    ElMessage.info('实时更新已暂停')
  }
}

const clearLogStream = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有日志数据吗？此操作将删除数据库中的所有日志记录，不可恢复！',
      '确认清空日志',
      {
        confirmButtonText: '确定清空',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 调用清空日志API
    clearing.value = true
    await systemLogsApi.cleanupSystemLogs(0) // 清理所有日志
    
    // 清空本地显示的日志
    logs.value = []
    
    // 重新加载日志以确保同步
    await loadSystemLogs()
    
    ElMessage.success('日志清空成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空日志失败:', error)
      ElMessage.error('清空日志失败')
    } else {
      ElMessage.info('已取消清空操作')
    }
  } finally {
    clearing.value = false
  }
}

const showLogDetails = (log: SystemLog) => {
  selectedLog.value = { ...log }
  detailDialogVisible.value = true
}

const handleDetailClose = () => {
  detailDialogVisible.value = false
  selectedLog.value = null
}

const copyLogEntry = async (log: SystemLog) => {
  try {
    const logText = `[${log.created_at}] ${log.level} ${log.module}: ${log.message}`
    await navigator.clipboard.writeText(logText)
    ElMessage.success('日志条目已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const copyLogDetails = async () => {
  if (!selectedLog.value) return

  try {
    const details = JSON.stringify(selectedLog.value, null, 2)
    await navigator.clipboard.writeText(details)
    ElMessage.success('日志详情已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const handleSizeChange = async (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  await loadSystemLogs()
}

const handleCurrentChange = async (page: number) => {
  pagination.currentPage = page
  await loadSystemLogs()
}

const formatTimestamp = (timestamp: string) => {
  return new Date(timestamp).toLocaleString()
}

const getLevelTagType = (level: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
  switch (level) {
    case 'DEBUG':
      return 'info'
    case 'INFO':
      return 'success'
    case 'WARNING':
      return 'warning'
    case 'ERROR':
    case 'CRITICAL':
      return 'danger'
    default:
      return 'info'
  }
}

// 定时轮询获取最新日志
const startPollingUpdates = async () => {
  if (pollingTimer) return

  // 检查认证状态
  if (!(await checkAuthStatus())) {
    connectionStatus.value = 'error'
    return
  }

  connectionStatus.value = 'connecting'

  const pollLogs = async () => {
    try {
      // 再次检查认证状态
      if (!(await checkAuthStatus())) {
        connectionStatus.value = 'error'
        stopPollingUpdates()
        return
      }

      const params: LogQueryParams = {
        page: 1,
        page_size: 20, // 每次获取最新的20条日志
        level: filters.level || undefined,
        module: filters.module || undefined
      }

      const response = await systemLogsApi.getSystemLogs(params)
      const newLogs = response.items

      if (newLogs.length > 0) {
        // 合并新日志，避免重复
        const existingIds = new Set(logs.value.map(log => log.id))
        const uniqueNewLogs = newLogs.filter(log => !existingIds.has(log.id))

        if (uniqueNewLogs.length > 0) {
          // 将新日志添加到列表顶部
          logs.value = [...uniqueNewLogs, ...logs.value]

          // 限制日志数量为100条
          if (logs.value.length > 100) {
            logs.value = logs.value.slice(0, 100)
          }

          // 自动滚动到顶部
          nextTick(() => {
            if (logStreamRef.value) {
              logStreamRef.value.scrollTop = 0
            }
          })
        }
      }

      connectionStatus.value = 'connected'
    } catch (error: any) {
      console.error('轮询获取日志失败:', error)

      if (error?.response?.status === 401) {
        ElMessage.warning('认证已过期，请重新登录')
        connectionStatus.value = 'error'
        stopPollingUpdates()
        realTimeEnabled.value = false
      } else {
        connectionStatus.value = 'error'
      }
    }
  }

  // 立即执行一次
  pollLogs()

  // 设置定时轮询
  pollingTimer = window.setInterval(pollLogs, pollingInterval)
}

// 停止定时轮询
const stopPollingUpdates = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer)
    pollingTimer = null
  }
  connectionStatus.value = 'disconnected'
}

// 实时更新相关
const startRealTimeUpdates = async () => {
  if (useWebSocket.value) {
    await startWebSocketUpdates()
  } else {
    await startPollingUpdates()
  }
}

const startWebSocketUpdates = async () => {
  if (wsManager) return

  const token = authStore.accessToken
  if (!token) {
    ElMessage.error('未找到认证令牌，无法启动实时日志流')
    connectionStatus.value = 'error'
    // 降级到轮询模式
    useWebSocket.value = false
    await startPollingUpdates()
    return
  }

  connectionStatus.value = 'connecting'
  wsManager = new LogWebSocketManager(token)

  // 设置事件监听器
  wsManager.on('connected', () => {
    console.log('实时日志流已连接')
    connectionStatus.value = 'connected'
    resetReconnectAttempts() // 连接成功后重置重连计数
  })

  wsManager.on('log', (logData: any) => {
    // 将新日志添加到列表顶部
    logs.value.unshift(logData)

    // 限制日志数量为100条
    if (logs.value.length > 100) {
      logs.value = logs.value.slice(0, 100)
    }

    // 自动滚动到顶部
    nextTick(() => {
      if (logStreamRef.value) {
        logStreamRef.value.scrollTop = 0
      }
    })
  })

  wsManager.on('error', async (error: any) => {
    console.error('WebSocket错误:', error)
    connectionStatus.value = 'error'

    if (reconnectAttempts.value < maxReconnectAttempts) {
      // 尝试重连
      stopWebSocketUpdates()
      scheduleReconnect()
    } else {
      // 重连次数用完，降级到轮询模式
      ElMessage.warning('WebSocket连接失败，切换到轮询模式')
      useWebSocket.value = false
      stopWebSocketUpdates()
      resetReconnectAttempts()
      await startPollingUpdates()
    }
  })

  wsManager.on('disconnected', () => {
    console.log('实时日志流已断开')
    connectionStatus.value = 'disconnected'

    // 如果是意外断开且实时更新仍然启用，尝试重连
    if (realTimeEnabled.value && reconnectAttempts.value < maxReconnectAttempts) {
      scheduleReconnect()
    }
  })

  // 建立连接
  try {
    wsManager.connect({
      level: filters.level || 'ALL',
      module: filters.module || 'ALL'
    })
  } catch (error) {
    console.error('WebSocket连接失败:', error)
    connectionStatus.value = 'error'
    // 降级到轮询模式
    useWebSocket.value = false
    startPollingUpdates()
  }
}

const stopWebSocketUpdates = () => {
  if (wsManager) {
    wsManager.disconnect()
    wsManager = null
  }
}

const stopRealTimeUpdates = () => {
  stopWebSocketUpdates()
  stopPollingUpdates()
  stopMockRealTimeUpdates()
}

// 日志配置相关方法
const handleConfigClose = () => {
  showConfigDialog.value = false
}

const loadLogFiles = (logType: string) => {
  // 模拟加载日志文件列表
  const mockFiles = [
    { name: `${logType}-2024-01-15.log`, size: '2.3MB' },
    { name: `${logType}-2024-01-14.log`, size: '1.8MB' },
    { name: `${logType}-2024-01-13.log`, size: '2.1MB' }
  ]
  logConfig.logFiles = mockFiles
  logConfig.selectedLogFile = ''
  logConfig.logContent = ''
}

const loadLogContent = (fileName: string) => {
  // 模拟加载日志内容
  const mockContent = `[2024-01-15 10:30:15] INFO system: 系统启动完成
[2024-01-15 10:30:16] INFO database: 数据库连接建立
[2024-01-15 10:30:17] INFO api: API服务启动
[2024-01-15 10:30:18] WARNING cache: 缓存预热中
[2024-01-15 10:30:19] INFO system: 所有服务就绪`
  logConfig.logContent = mockContent
}

const downloadLogFile = async () => {
  if (!logConfig.selectedLogFile) return

  try {
    // 模拟下载日志文件
    const blob = new Blob([logConfig.logContent], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = logConfig.selectedLogFile
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    ElMessage.success('日志文件下载成功')
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

const clearLogFile = async () => {
  if (!logConfig.selectedLogFile) return

  try {
    await ElMessageBox.confirm(`确定要清空日志文件 ${logConfig.selectedLogFile} 吗？`, '确认清空', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    logConfig.logContent = ''
    ElMessage.success('日志文件已清空')
  } catch {
    // 用户取消
  }
}

const saveLogConfig = async () => {
  try {
    configSaving.value = true
    // 模拟保存配置
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('日志配置保存成功')
    showConfigDialog.value = false
  } catch (error) {
    ElMessage.error('配置保存失败')
  } finally {
    configSaving.value = false
  }
}

const resetLogConfig = () => {
  logConfig.logLevel = 'INFO'
  logConfig.logFormat = 'standard'
  logConfig.logFilePath = '/var/log/wiscude/'
  logConfig.maxFileSize = 100
  logConfig.retentionDays = 30
  logConfig.enableRotation = true
  logConfig.enabledLogTypes = ['system', 'access', 'error', 'security']
  logConfig.viewLogType = ''
  logConfig.selectedLogFile = ''
  logConfig.logFiles = []
  logConfig.logContent = ''
  ElMessage.success('配置已重置')
}

// 生命周期
onMounted(async () => {
  try {
    // 首先检查认证状态
    const isAuthenticated = await checkAuthStatus()

    if (isAuthenticated) {
      // 用户已认证，加载真实数据
      await loadLogModules()
      await loadSystemLogs()

      if (realTimeEnabled.value) {
        await startRealTimeUpdates()
      }
    } else {
      // 用户未认证，直接启用演示模式
      console.log('用户未认证，启用演示模式')
      enableFallbackMode()

      ElMessage({
        message: '欢迎使用日志管理系统！当前为演示模式，登录后可查看真实数据。',
        type: 'info',
        duration: 5000,
        showClose: true
      })
    }
  } catch (error) {
    console.error('页面初始化失败:', error)
    // 初始化失败，启用降级模式
    enableFallbackMode()
  }
})

onUnmounted(() => {
  stopRealTimeUpdates()
  resetReconnectAttempts()
  stopMockRealTimeUpdates()
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
})
</script>

<style scoped>
@import '@/styles/design-system.scss';

.log-management {
  padding: var(--spacing-4);

  /* 控制面板 */
  .control-panel {
    margin-bottom: var(--spacing-6);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-4);

      h3 {
        margin: 0;
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
      }

      .panel-actions {
        display: flex;
        gap: var(--spacing-3);

        .el-button {
          border-radius: var(--radius-lg);
          font-weight: var(--font-weight-medium);
        }
      }
    }


  }

  /* 日志统计 */
  .log-statistics {
    margin-bottom: var(--spacing-6);

    .stat-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
      }

      &.info {
        border-left: 4px solid var(--primary-color);
      }

      &.success {
        border-left: 4px solid var(--success-color);
      }

      &.warning {
        border-left: 4px solid var(--warning-color);
      }

      &.error {
        border-left: 4px solid var(--error-color);
      }

      .stat-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-5);

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: var(--radius-lg);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-lg);
        }

        .stat-info {
          flex: 1;

          .stat-value {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .stat-label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-2);
          }

          .stat-trend {
            display: flex;
            align-items: center;
            gap: var(--spacing-1);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);

            &.positive {
              color: var(--success-color);
            }

            &.negative {
              color: var(--error-color);
            }
          }
        }
      }
    }
  }

  /* 日志流 */
  .log-stream {
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);

    .el-card__header {
      border-bottom: 1px solid var(--border-light);

      .stream-header {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-4);

        .stream-title {
          display: flex;
          align-items: center;
          gap: var(--spacing-3);

          .stream-status {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
          }
        }

        .log-filters {
          .el-form {
            .el-form-item {
              margin-bottom: var(--spacing-3);
              margin-right: var(--spacing-4);

              .el-form-item__label {
                font-weight: var(--font-weight-medium);
                color: var(--text-primary);
              }

              .el-select,
              .el-date-editor,
              .el-input {
                border-radius: var(--radius-lg);
              }
            }
          }
        }

        .stream-controls {
          display: flex;
          align-items: center;
          gap: var(--spacing-2);
          flex-wrap: wrap;
        }
      }
    }

    .log-stream-container {
      max-height: 600px;
      overflow-y: auto;
      border-radius: var(--radius-lg);
      background: var(--bg-light);

      .log-entry {
        display: grid;
        grid-template-columns: 180px 80px 100px 1fr 80px;
        gap: var(--spacing-3);
        padding: var(--spacing-3) var(--spacing-4);
        border-bottom: 1px solid var(--border-light);
        transition: all 0.2s ease;
        cursor: pointer;

        &:hover {
          background: var(--bg-secondary);
        }

        &:last-child {
          border-bottom: none;
        }

        .log-timestamp {
          font-size: var(--font-size-xs);
          color: var(--text-secondary);
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }

        .log-level {
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-bold);
          padding: 2px 6px;
          border-radius: var(--radius-sm);
          text-align: center;

          &.level-debug {
            background: var(--bg-secondary);
            color: var(--text-secondary);
          }

          &.level-info {
            background: var(--success-light);
            color: var(--success-color);
          }

          &.level-warning {
            background: var(--warning-light);
            color: var(--warning-color);
          }

          &.level-error,
          &.level-critical {
            background: var(--error-light);
            color: var(--error-color);
          }
        }

        .log-module {
          font-size: var(--font-size-xs);
          color: var(--text-tertiary);
          font-weight: var(--font-weight-medium);
        }

        .log-message {
          font-size: var(--font-size-sm);
          color: var(--text-primary);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .log-actions {
          display: flex;
          gap: var(--spacing-1);
          opacity: 0;
          transition: opacity 0.2s ease;

          .el-button {
            padding: 4px;
          }
        }

        &:hover .log-actions {
          opacity: 1;
        }
      }

      .empty-logs {
        padding: var(--spacing-8);
        text-align: center;

        .empty-icon {
          font-size: 48px;
          margin-bottom: var(--spacing-4);
        }

        .empty-text {
          p {
            margin: var(--spacing-2) 0;
            color: var(--text-secondary);
          }

          .empty-hint {
            font-size: var(--font-size-sm);
            color: var(--text-tertiary);
          }
        }
      }
    }

    .log-pagination {
      padding: var(--spacing-4);
      border-top: 1px solid var(--border-light);
      display: flex;
      justify-content: center;
    }
  }

  /* 日志详情对话框 */
  .log-detail {
    .log-message-detail,
    .log-stack-trace {
      margin-top: var(--spacing-4);

      h4 {
        margin: 0 0 var(--spacing-3) 0;
        font-size: var(--font-size-base);
        color: var(--text-primary);
      }

      .log-message-textarea,
      .stack-trace-textarea {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: var(--font-size-sm);
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-3);
  }
}

/* 响应式设计 */
@include respond-to('lg') {
  .log-management {
    .control-panel {
      .panel-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-3);
      }

      .log-filters .el-form .el-form-item {
        margin-right: 0;
        margin-bottom: var(--spacing-4);
      }
    }

    .log-statistics .el-col {
      margin-bottom: var(--spacing-4);
    }

    .log-stream .el-card__header .stream-header {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-3);

      .stream-controls {
        width: 100%;
        justify-content: flex-start;
      }
    }
  }
}

@include respond-to('md') {
  .log-management {
    padding: var(--spacing-3);

    .log-stream .log-stream-container .log-entry {
      grid-template-columns: 1fr;
      gap: var(--spacing-2);

      .log-timestamp,
      .log-level,
      .log-module {
        font-size: var(--font-size-xs);
      }

      .log-actions {
        opacity: 1;
        justify-content: flex-end;
      }
    }
  }
}

@include respond-to('sm') {
  .log-management {
    .log-stream .el-card__header .stream-header {
      .log-filters .el-form {
        .el-form-item {
          width: 100%;
          margin-right: 0;

          .el-select,
          .el-date-editor,
          .el-input {
            width: 100%;
          }
        }
      }
    }
  }
}

/* 日志配置面板样式 */
.log-config-panel {
  margin-bottom: 24px;

  .config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .config-item {
    .config-label {
      font-size: 12px;
      color: var(--text-secondary);
      margin-bottom: 4px;
    }

    .config-value {
      font-size: 14px;
      font-weight: 500;
      color: var(--text-primary);
    }
  }
}
</style>
