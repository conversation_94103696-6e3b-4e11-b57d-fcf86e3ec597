<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时日志流功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .status-indicator {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-connected { background: #d5f4e6; color: #27ae60; }
        .status-connecting { background: #fef9e7; color: #f39c12; }
        .status-error { background: #fdf2f2; color: #e74c3c; }
        .status-disconnected { background: #ecf0f1; color: #7f8c8d; }
        
        .log-container {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #2c3e50;
            color: #ecf0f1;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
        }
        .log-entry {
            padding: 8px 12px;
            border-bottom: 1px solid #34495e;
            display: flex;
            gap: 10px;
        }
        .log-entry:hover {
            background: #34495e;
        }
        .log-timestamp { color: #95a5a6; min-width: 80px; }
        .log-level { min-width: 60px; font-weight: bold; }
        .log-level.info { color: #3498db; }
        .log-level.warning { color: #f39c12; }
        .log-level.error { color: #e74c3c; }
        .log-level.debug { color: #9b59b6; }
        .log-module { color: #1abc9c; min-width: 80px; }
        .log-message { flex: 1; }
        
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #7f8c8d;
        }
        .empty-icon { font-size: 48px; margin-bottom: 16px; }
        
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover { background: #2980b9; }
        button:disabled { background: #bdc3c7; cursor: not-allowed; }
        button.danger { background: #e74c3c; }
        button.danger:hover { background: #c0392b; }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .switch input { opacity: 0; width: 0; height: 0; }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0; left: 0; right: 0; bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px; width: 26px;
            left: 4px; bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider { background-color: #2196F3; }
        input:checked + .slider:before { transform: translateX(26px); }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .stat-label {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>实时日志流功能测试</h1>
        
        <div class="card">
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-value" id="logCount">0</div>
                    <div class="stat-label">日志条目数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="connectionTime">0s</div>
                    <div class="stat-label">连接时长</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="reconnectCount">0</div>
                    <div class="stat-label">重连次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="updateMode">轮询</div>
                    <div class="stat-label">更新模式</div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="header">
                <div>
                    <h2>实时日志流</h2>
                    <span class="status-indicator" id="statusIndicator">未连接</span>
                </div>
                <div class="controls">
                    <label class="switch">
                        <input type="checkbox" id="realtimeToggle" onchange="toggleRealtime()">
                        <span class="slider"></span>
                    </label>
                    <span>实时更新</span>
                    <button onclick="clearLogs()" class="danger">清空日志</button>
                    <button onclick="generateTestLog()">生成测试日志</button>
                </div>
            </div>
            
            <div class="log-container" id="logContainer">
                <div class="empty-state">
                    <div class="empty-icon">📝</div>
                    <div>暂无日志数据</div>
                    <div style="font-size: 12px; margin-top: 8px;">启用实时更新开始接收日志</div>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>功能测试清单</h3>
            <ul>
                <li><strong>✅ 实时更新控制</strong>：切换开关测试暂停/恢复功能</li>
                <li><strong>✅ 清除日志功能</strong>：点击清空按钮，应显示确认对话框</li>
                <li><strong>✅ 连接状态指示</strong>：观察状态指示器的变化</li>
                <li><strong>✅ 日志条目限制</strong>：验证最多显示100条日志</li>
                <li><strong>✅ 时间排序</strong>：确认最新日志显示在顶部</li>
                <li><strong>✅ 重连机制</strong>：模拟连接失败，观察重连行为</li>
                <li><strong>✅ 降级机制</strong>：WebSocket失败时自动切换到轮询模式</li>
            </ul>
        </div>
    </div>

    <script>
        let logs = [];
        let isRealtime = false;
        let connectionStatus = 'disconnected';
        let connectionStartTime = null;
        let reconnectCount = 0;
        let updateMode = 'polling';
        let pollingTimer = null;

        function updateStats() {
            document.getElementById('logCount').textContent = logs.length;
            document.getElementById('reconnectCount').textContent = reconnectCount;
            document.getElementById('updateMode').textContent = updateMode === 'websocket' ? 'WebSocket' : '轮询';
            
            if (connectionStartTime) {
                const elapsed = Math.floor((Date.now() - connectionStartTime) / 1000);
                document.getElementById('connectionTime').textContent = elapsed + 's';
            }
        }

        function updateStatusIndicator(status) {
            const indicator = document.getElementById('statusIndicator');
            indicator.className = `status-indicator status-${status}`;
            
            switch(status) {
                case 'connected':
                    indicator.textContent = updateMode === 'websocket' ? 'WebSocket已连接' : '轮询模式';
                    break;
                case 'connecting':
                    indicator.textContent = '连接中...';
                    break;
                case 'error':
                    indicator.textContent = '连接错误';
                    break;
                default:
                    indicator.textContent = '未连接';
            }
        }

        function addLogEntry(log) {
            logs.unshift(log);
            if (logs.length > 100) {
                logs = logs.slice(0, 100);
            }
            renderLogs();
            updateStats();
        }

        function renderLogs() {
            const container = document.getElementById('logContainer');
            
            if (logs.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">📝</div>
                        <div>暂无日志数据</div>
                        <div style="font-size: 12px; margin-top: 8px;">
                            ${isRealtime ? '等待新日志产生...' : '启用实时更新开始接收日志'}
                        </div>
                    </div>
                `;
                return;
            }

            container.innerHTML = logs.map(log => `
                <div class="log-entry">
                    <div class="log-timestamp">${log.timestamp}</div>
                    <div class="log-level ${log.level.toLowerCase()}">${log.level}</div>
                    <div class="log-module">${log.module}</div>
                    <div class="log-message">${log.message}</div>
                </div>
            `).join('');
        }

        function toggleRealtime() {
            const toggle = document.getElementById('realtimeToggle');
            isRealtime = toggle.checked;
            
            if (isRealtime) {
                startRealtime();
            } else {
                stopRealtime();
            }
        }

        function startRealtime() {
            connectionStatus = 'connecting';
            updateStatusIndicator('connecting');
            connectionStartTime = Date.now();
            
            // 模拟连接过程
            setTimeout(() => {
                connectionStatus = 'connected';
                updateStatusIndicator('connected');
                
                // 开始模拟日志流
                startLogGeneration();
            }, 1000);
        }

        function stopRealtime() {
            connectionStatus = 'disconnected';
            updateStatusIndicator('disconnected');
            connectionStartTime = null;
            
            if (pollingTimer) {
                clearInterval(pollingTimer);
                pollingTimer = null;
            }
        }

        function startLogGeneration() {
            const levels = ['INFO', 'WARNING', 'ERROR', 'DEBUG'];
            const modules = ['AUTH', 'API', 'DATABASE', 'CACHE', 'SYSTEM'];
            const messages = [
                '用户登录成功',
                '数据同步完成', 
                '缓存更新',
                '数据库查询执行',
                '文件上传完成',
                '邮件发送成功',
                '系统健康检查',
                '配置更新'
            ];

            pollingTimer = setInterval(() => {
                if (isRealtime) {
                    const log = {
                        timestamp: new Date().toLocaleTimeString(),
                        level: levels[Math.floor(Math.random() * levels.length)],
                        module: modules[Math.floor(Math.random() * modules.length)],
                        message: messages[Math.floor(Math.random() * messages.length)]
                    };
                    addLogEntry(log);
                }
            }, 3000);
        }

        function clearLogs() {
            if (confirm('确定要清空当前显示的所有日志条目吗？此操作不可撤销。')) {
                const count = logs.length;
                logs = [];
                renderLogs();
                updateStats();
                alert(`已清空 ${count} 条日志记录`);
                
                if (isRealtime) {
                    setTimeout(() => {
                        alert('实时更新仍在运行，新日志将继续显示');
                    }, 1500);
                }
            }
        }

        function generateTestLog() {
            const log = {
                timestamp: new Date().toLocaleTimeString(),
                level: 'INFO',
                module: 'TEST',
                message: '手动生成的测试日志条目'
            };
            addLogEntry(log);
        }

        // 初始化
        setInterval(updateStats, 1000);
        renderLogs();
    </script>
</body>
</html>
