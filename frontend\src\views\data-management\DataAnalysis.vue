<template>
  <div class="data-analysis-dashboard">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1>数据分析中心</h1>
          <p>深度洞察学习数据，驱动教育决策优化</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="exportReport" :loading="exporting">
            <el-icon><Download /></el-icon>
            导出报告
          </el-button>
          <el-button @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button @click="toggleRealTime" :type="isRealTimeEnabled ? 'success' : 'info'">
            <el-icon><Connection /></el-icon>
            {{ isRealTimeEnabled ? '实时更新中' : '开启实时更新' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 高级筛选器 -->
    <el-card class="filter-card">
      <div class="filter-content">
        <el-form :model="filters" inline>
          <!-- 快速时间选择 -->
          <el-form-item label="快速选择">
            <el-radio-group v-model="quickTimeRange" @change="handleQuickTimeChange">
              <el-radio-button label="today">今日</el-radio-button>
              <el-radio-button label="week">本周</el-radio-button>
              <el-radio-button label="month">本月</el-radio-button>
              <el-radio-button label="custom">自定义</el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <!-- 自定义时间范围 -->
          <el-form-item label="时间范围" v-if="quickTimeRange === 'custom'">
            <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
            />
          </el-form-item>
          
          <!-- 用户群体筛选 -->
          <el-form-item label="用户群体">
            <el-select v-model="filters.userGroup" placeholder="选择用户群体" @change="handleFilterChange">
              <el-option label="全部用户" value="all" />
              <el-option label="新用户" value="new" />
              <el-option label="活跃用户" value="active" />
              <el-option label="付费用户" value="paid" />
              <el-option label="流失用户" value="churned" />
            </el-select>
          </el-form-item>
          
          <!-- 设备类型筛选 -->
          <el-form-item label="设备类型">
            <el-select v-model="filters.deviceType" placeholder="选择设备类型" @change="handleFilterChange">
              <el-option label="全部设备" value="all" />
              <el-option label="移动端" value="mobile" />
              <el-option label="桌面端" value="desktop" />
              <el-option label="平板" value="tablet" />
            </el-select>
          </el-form-item>
          
          <!-- 地域筛选 -->
          <el-form-item label="地域分布">
            <el-select v-model="filters.region" placeholder="选择地域" @change="handleFilterChange">
              <el-option label="全部地区" value="all" />
              <el-option label="华北地区" value="north" />
              <el-option label="华东地区" value="east" />
              <el-option label="华南地区" value="south" />
              <el-option label="华中地区" value="central" />
              <el-option label="西部地区" value="west" />
            </el-select>
          </el-form-item>

          <!-- 用户标签筛选 -->
          <el-form-item label="用户标签">
            <el-select
              v-model="filters.userTags"
              multiple
              placeholder="选择用户标签"
              @change="handleFilterChange"
              style="width: 200px;"
            >
              <el-option label="高活跃" value="high_active" />
              <el-option label="新手用户" value="beginner" />
              <el-option label="VIP用户" value="vip" />
              <el-option label="长期用户" value="long_term" />
              <el-option label="潜在流失" value="potential_churn" />
            </el-select>
          </el-form-item>

          <!-- 学习阶段筛选 -->
          <el-form-item label="学习阶段">
            <el-select v-model="filters.learningStage" placeholder="选择学习阶段" @change="handleFilterChange">
              <el-option label="全部阶段" value="all" />
              <el-option label="入门阶段" value="beginner" />
              <el-option label="进阶阶段" value="intermediate" />
              <el-option label="高级阶段" value="advanced" />
              <el-option label="专家阶段" value="expert" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="applyFilters">应用筛选</el-button>
            <el-button @click="resetFilters">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 核心统计指标 -->
    <div class="stats-overview">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :lg="6" v-for="(stat, index) in enhancedStatistics" :key="index">
          <el-card :class="['stat-card', stat.type]" @click="handleStatClick(stat)">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><component :is="stat.icon" /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value" :class="{ 'animate-number': stat.isAnimating }">
                  {{ formatNumber(stat.value) }}
                </div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-trend" :class="stat.growth >= 0 ? 'positive' : 'negative'">
                  <el-icon><ArrowUp v-if="stat.growth >= 0" /><ArrowDown v-else /></el-icon>
                  {{ Math.abs(stat.growth) }}%
                  <span class="trend-period">{{ stat.period }}</span>
                </div>
                <div class="stat-subtitle">{{ stat.subtitle }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据可视化图表区域 -->
    <div class="charts-section">
      <!-- 第一行：用户增长趋势和实时活跃度 -->
      <el-row :gutter="24">
        <el-col :xs="24" :lg="16">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>用户增长趋势</span>
                <div class="chart-controls">
                  <el-select v-model="userTrendPeriod" size="small" @change="updateUserTrend">
                    <el-option label="最近7天" value="7d" />
                    <el-option label="最近30天" value="30d" />
                    <el-option label="最近90天" value="90d" />
                  </el-select>
                  <el-button size="small" @click="toggleTrendComparison">
                    {{ showComparison ? '隐藏对比' : '显示对比' }}
                  </el-button>
                </div>
              </div>
            </template>
            <div class="chart-container">
              <LineChart
                :data="transformToSeriesData(userTrendData)"
                :options="userTrendOptions"
                height="350px"
                @chart-click="handleChartClick"
              />
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <span>实时活跃度</span>
            </template>
            <div class="chart-container">
              <div class="real-time-stats">
                <div class="real-time-item" v-for="item in realTimeStats" :key="item.label">
                  <div class="real-time-label">{{ item.label }}</div>
                  <div class="real-time-value" :style="{ color: item.color }">
                    {{ item.value }}
                  </div>
                  <div class="real-time-change" :class="item.change >= 0 ? 'positive' : 'negative'">
                    {{ item.change >= 0 ? '+' : '' }}{{ item.change }}%
                  </div>
                </div>
              </div>
              <div class="activity-pulse">
                <div class="pulse-dot"></div>
                <span>实时数据更新中</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 第二行：学习时长分布和课程完成率 -->
      <el-row :gutter="24" style="margin-top: 24px;">
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <span>学习时长分布</span>
            </template>
            <div class="chart-container">
              <PieChart
                :data="transformToDataItems(learningTimeData)"
                :options="learningTimeOptions"
                height="300px"
                @chart-click="handleChartClick"
              />
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>课程完成率统计</span>
                <el-button size="small" @click="toggleCourseView">
                  {{ showCourseDetails ? '简化视图' : '详细视图' }}
                </el-button>
              </div>
            </template>
            <div class="chart-container">
              <BarChart
                :data="transformToSeriesData(courseCompletionData)"
                :options="courseCompletionOptions"
                height="300px"
                @chart-click="handleChartClick"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 第三行：用户行为分析和设备分布 -->
      <el-row :gutter="24" style="margin-top: 24px;">
        <el-col :xs="24" :lg="16">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>用户行为热力图</span>
                <el-select v-model="heatmapType" size="small" @change="updateHeatmap">
                  <el-option label="学习活跃度" value="activity" />
                  <el-option label="功能使用" value="feature" />
                  <el-option label="访问路径" value="path" />
                </el-select>
              </div>
            </template>
            <div class="chart-container">
              <div class="behavior-heatmap">
                <div v-for="(day, index) in behaviorHeatmapData" :key="index" class="heatmap-row">
                  <div class="row-label">{{ day.label }}</div>
                  <div class="heatmap-cells">
                    <div
                      v-for="(cell, cellIndex) in day.data"
                      :key="cellIndex"
                      class="heatmap-cell"
                      :style="{
                        backgroundColor: getHeatmapColor(cell.value),
                        opacity: Math.max(0.1, Math.min(1, cell.value / 100))
                      }"
                      :title="`${day.label} ${cell.hour}:00 - ${cell.value}%`"
                      @click="handleHeatmapClick(day, cell)"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <span>设备分布统计</span>
            </template>
            <div class="chart-container">
              <DoughnutChart
                :data="transformToDoughnutData(deviceDistributionData)"
                :options="deviceDistributionOptions"
                height="300px"
                @chart-click="handleChartClick"
              />
              <div class="device-stats">
                <div v-for="device in deviceStats" :key="device.type" class="device-stat-item">
                  <div class="device-icon" :style="{ backgroundColor: device.color }">
                    <el-icon><component :is="device.icon" /></el-icon>
                  </div>
                  <div class="device-info">
                    <div class="device-name">{{ device.name }}</div>
                    <div class="device-percentage">{{ device.percentage }}%</div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 第四行：用户路径分析和生命周期分析 -->
      <el-row :gutter="24" style="margin-top: 24px;">
        <el-col :xs="24" :lg="14">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>用户路径流向分析</span>
                <el-select v-model="pathAnalysisType" size="small" @change="updatePathAnalysis">
                  <el-option label="页面流向" value="page" />
                  <el-option label="功能使用" value="feature" />
                  <el-option label="学习路径" value="learning" />
                </el-select>
              </div>
            </template>
            <div class="chart-container">
              <div class="user-path-analysis">
                <div class="path-flow">
                  <div v-for="(step, index) in userPathData" :key="index" class="path-step">
                    <div class="step-node" :style="{ backgroundColor: step.color }">
                      <div class="step-name">{{ step.name }}</div>
                      <div class="step-count">{{ step.count }}</div>
                    </div>
                    <div v-if="index < userPathData.length - 1" class="path-arrow">
                      <div class="arrow-line" :style="{ width: step.flowWidth + 'px' }"></div>
                      <div class="arrow-head"></div>
                      <div class="flow-percentage">{{ step.flowPercentage }}%</div>
                    </div>
                  </div>
                </div>
                <div class="path-insights">
                  <h4>路径洞察</h4>
                  <ul>
                    <li v-for="insight in pathInsights" :key="insight.id">
                      <el-icon><TrendCharts /></el-icon>
                      {{ insight.text }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :lg="10">
          <el-card class="chart-card">
            <template #header>
              <span>用户生命周期分析</span>
            </template>
            <div class="chart-container">
              <div class="lifecycle-analysis">
                <div v-for="stage in lifecycleStages" :key="stage.name" class="lifecycle-stage">
                  <div class="stage-header">
                    <div class="stage-icon" :style="{ backgroundColor: stage.color }">
                      <el-icon><component :is="stage.icon" /></el-icon>
                    </div>
                    <div class="stage-info">
                      <div class="stage-name">{{ stage.name }}</div>
                      <div class="stage-description">{{ stage.description }}</div>
                    </div>
                  </div>
                  <div class="stage-metrics">
                    <div class="metric-item">
                      <span class="metric-label">用户数量</span>
                      <span class="metric-value">{{ stage.userCount }}</span>
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">平均停留</span>
                      <span class="metric-value">{{ stage.avgDuration }}</span>
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">转化率</span>
                      <span class="metric-value" :class="stage.conversionRate >= 50 ? 'positive' : 'negative'">
                        {{ stage.conversionRate }}%
                      </span>
                    </div>
                  </div>
                  <div class="stage-progress">
                    <el-progress
                      :percentage="stage.conversionRate"
                      :color="stage.color"
                      :show-text="false"
                      :stroke-width="6"
                    />
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 第五行：学习活跃度热力图和数据导出 -->
      <el-row :gutter="24" style="margin-top: 24px;">
        <el-col :xs="24" :lg="16">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>学习活跃度分布</span>
                <div class="chart-controls">
                  <el-select v-model="activityViewType" size="small" @change="updateActivityView">
                    <el-option label="按小时分布" value="hourly" />
                    <el-option label="按天分布" value="daily" />
                    <el-option label="按周分布" value="weekly" />
                  </el-select>
                  <el-button size="small" @click="toggleActivityAnimation">
                    {{ activityAnimationEnabled ? '暂停动画' : '开启动画' }}
                  </el-button>
                </div>
              </div>
            </template>
            <div class="chart-container">
              <div class="activity-heatmap">
                <div v-for="(day, index) in activityData" :key="index" class="activity-day">
                  <div class="day-label">{{ day.label }}</div>
                  <div class="activity-bars">
                    <div
                      v-for="(hour, hourIndex) in day.hours"
                      :key="hourIndex"
                      class="activity-bar"
                      :class="{ 'animate-bar': activityAnimationEnabled }"
                      :style="{
                        height: hour.value + '%',
                        backgroundColor: getActivityColor(hour.value),
                        animationDelay: (hourIndex * 50) + 'ms'
                      }"
                      :title="`${day.label} ${hour.hour}:00 - ${hour.value}%活跃度`"
                      @click="handleActivityBarClick(day, hour)"
                    ></div>
                  </div>
                </div>
              </div>
              <div class="activity-legend">
                <div class="legend-title">活跃度等级</div>
                <div class="legend-items">
                  <div v-for="level in activityLevels" :key="level.label" class="legend-item">
                    <div class="legend-color" :style="{ backgroundColor: level.color }"></div>
                    <span class="legend-label">{{ level.label }}</span>
                    <span class="legend-range">{{ level.range }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>数据导出中心</span>
              </div>
            </template>
            <div class="chart-container">
              <div class="export-center">
                <div class="export-actions">
                  <el-button type="primary" @click="showExportDialog = true">
                    <el-icon><Download /></el-icon>
                    导出报告
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 实时数据更新提示 -->
    <div class="real-time-indicator" v-if="isRealTimeEnabled">
      <el-icon class="pulse"><Connection /></el-icon>
      <span>实时数据更新中...</span>
      <span class="last-update">最后更新: {{ lastUpdateTime }}</span>
    </div>
  </div>

  <!-- 导出报告弹窗 -->
  <el-dialog
    v-model="showExportDialog"
    title="导出报告"
    width="600px"
    :before-close="handleCloseExportDialog"
  >
    <div class="export-dialog-content">
      <div class="option-group">
        <h4>导出格式</h4>
        <el-radio-group v-model="exportFormat">
          <el-radio label="excel">Excel</el-radio>
          <el-radio label="pdf">PDF</el-radio>
          <el-radio label="csv">CSV</el-radio>
          <el-radio label="json">JSON</el-radio>
        </el-radio-group>
      </div>

      <div class="option-group">
        <h4>数据范围</h4>
        <el-checkbox-group v-model="exportDataTypes">
          <el-checkbox label="statistics">统计数据</el-checkbox>
          <el-checkbox label="charts">图表数据</el-checkbox>
          <el-checkbox label="user_behavior">用户行为</el-checkbox>
          <el-checkbox label="performance">性能数据</el-checkbox>
        </el-checkbox-group>
      </div>

      <div class="option-group">
        <h4>时间范围</h4>
        <el-date-picker
          v-model="exportDateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="default"
        />
      </div>

      <div class="export-history">
        <h4>最近导出</h4>
        <div class="history-list">
          <div v-for="(item, index) in exportHistory" :key="index" class="history-item">
            <div class="history-info">
              <div class="history-name">{{ item.name }}</div>
              <div class="history-time">{{ formatTime(item.time) }}</div>
            </div>
            <div class="history-actions">
              <el-button size="small" text @click="downloadHistoryItem(item)">
                <el-icon><Download /></el-icon>
              </el-button>
              <el-button size="small" text @click="deleteHistoryItem(index)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showExportDialog = false">取消</el-button>
        <el-button @click="handleScheduleExport">
          <el-icon><Clock /></el-icon>
          定时导出
        </el-button>
        <el-button @click="handleEmailReport">
          <el-icon><Message /></el-icon>
          邮件报告
        </el-button>
        <el-button type="primary" @click="handleExportData" :loading="exportLoading">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Download, Refresh, User, TrendCharts, Document, Clock,
  ArrowUp, ArrowDown, Connection, Monitor, DataLine, Star, Message, Delete
} from '@element-plus/icons-vue'
import { LineChart, PieChart, BarChart, DoughnutChart } from '@/components/charts'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import {
  transformToSeriesData,
  transformToDataItems,
  transformToDoughnutData
} from '@/utils/chartDataTransform'

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const isRealTimeEnabled = ref(true)
const lastUpdateTime = ref('')
const userTrendPeriod = ref('30d')
const quickTimeRange = ref('month')
const showComparison = ref(false)
const showCourseDetails = ref(false)
const heatmapType = ref('activity')
const pathAnalysisType = ref('page')
const activityViewType = ref('hourly')
const activityAnimationEnabled = ref(true)
const showExportOptions = ref(false)
const showExportDialog = ref(false)
const exportFormat = ref('excel')
const exportDataTypes = ref(['statistics', 'charts'])
const exportDateRange = ref<[Date, Date] | undefined>(undefined)
const exportLoading = ref(false)

// 筛选器
const filters = reactive({
  dateRange: [] as string[],
  userGroup: 'all',
  deviceType: 'all',
  region: 'all',
  userTags: [] as string[],
  learningStage: 'all'
})

// 增强的统计数据
const enhancedStatistics = ref([
  {
    label: '总用户数',
    value: 15847,
    growth: 12.5,
    period: '较上月',
    subtitle: '新增用户1,234',
    icon: 'User',
    type: 'primary',
    isAnimating: false
  },
  {
    label: '活跃用户',
    value: 8923,
    growth: 8.3,
    period: '较上周',
    subtitle: '活跃率56.3%',
    icon: 'TrendCharts',
    type: 'success',
    isAnimating: false
  },
  {
    label: '用户留存率',
    value: 73.2,
    growth: 5.1,
    period: '较上月',
    subtitle: '7日留存率',
    icon: 'Star',
    type: 'warning',
    isAnimating: false
  },
  {
    label: '平均会话时长',
    value: 24.5,
    growth: -2.1,
    period: '较上周',
    subtitle: '分钟/会话',
    icon: 'Clock',
    type: 'info',
    isAnimating: false
  }
])

// 实时统计数据
const realTimeStats = ref([
  { label: '在线用户', value: 1234, change: 5.2, color: '#10b981' },
  { label: '今日新增', value: 89, change: -2.1, color: '#3b82f6' },
  { label: '活跃会话', value: 567, change: 8.7, color: '#f59e0b' }
])

// 学习时长分布数据
const learningTimeData = ref({
  labels: ['0-1小时', '1-3小时', '3-5小时', '5-8小时', '8小时以上'],
  datasets: [{
    data: [25, 35, 20, 15, 5],
    backgroundColor: [
      '#4f46e5',
      '#06b6d4',
      '#10b981',
      '#f59e0b',
      '#ef4444'
    ],
    borderWidth: 2,
    borderColor: '#ffffff'
  }]
})

const learningTimeOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
    },
    tooltip: {
      callbacks: {
        label: (context: any) => {
          return `${context.label}: ${context.parsed}%`
        }
      }
    }
  }
})

// 课程完成率数据
const courseCompletionData = ref({
  labels: ['数学', '英语', '科学', '编程', '艺术', '体育'],
  datasets: [{
    label: '完成率 (%)',
    data: [85, 78, 92, 67, 73, 88],
    backgroundColor: 'rgba(79, 70, 229, 0.8)',
    borderColor: '#4f46e5',
    borderWidth: 2,
    borderRadius: 4
  }]
})

const courseCompletionOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    },
    tooltip: {
      callbacks: {
        afterLabel: () => '点击查看详细数据'
      }
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      max: 100,
      title: {
        display: true,
        text: '完成率 (%)'
      }
    },
    x: {
      title: {
        display: true,
        text: '课程类型'
      }
    }
  }
})

// 用户行为热力图数据
const behaviorHeatmapData = ref([
  {
    label: '周一',
    data: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.floor(Math.random() * 100)
    }))
  },
  {
    label: '周二',
    data: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.floor(Math.random() * 100)
    }))
  },
  {
    label: '周三',
    data: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.floor(Math.random() * 100)
    }))
  },
  {
    label: '周四',
    data: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.floor(Math.random() * 100)
    }))
  },
  {
    label: '周五',
    data: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.floor(Math.random() * 100)
    }))
  },
  {
    label: '周六',
    data: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.floor(Math.random() * 100)
    }))
  },
  {
    label: '周日',
    data: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.floor(Math.random() * 100)
    }))
  }
])

// 设备分布数据
const deviceDistributionData = ref({
  labels: ['移动端', '桌面端', '平板'],
  datasets: [{
    data: [65, 28, 7],
    backgroundColor: [
      '#4f46e5',
      '#10b981',
      '#f59e0b'
    ],
    borderWidth: 3,
    borderColor: '#ffffff'
  }]
})

const deviceDistributionOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  cutout: '60%',
  plugins: {
    legend: {
      display: false
    },
    tooltip: {
      callbacks: {
        label: (context: any) => {
          return `${context.label}: ${context.parsed}%`
        }
      }
    }
  }
})

// 设备统计数据
const deviceStats = ref([
  { type: 'mobile', name: '移动端', percentage: 65, color: '#4f46e5', icon: 'Monitor' },
  { type: 'desktop', name: '桌面端', percentage: 28, color: '#10b981', icon: 'Monitor' },
  { type: 'tablet', name: '平板', percentage: 7, color: '#f59e0b', icon: 'Monitor' }
])

// 用户路径分析数据
const userPathData = ref([
  { name: '首页', count: 1000, color: '#4f46e5', flowWidth: 80, flowPercentage: 85 },
  { name: '课程列表', count: 850, color: '#10b981', flowWidth: 60, flowPercentage: 70 },
  { name: '课程详情', count: 595, color: '#f59e0b', flowWidth: 40, flowPercentage: 45 },
  { name: '开始学习', count: 268, color: '#ef4444', flowWidth: 20, flowPercentage: 25 },
  { name: '完成学习', count: 67, color: '#8b5cf6', flowWidth: 0, flowPercentage: 0 }
])

// 路径洞察数据
const pathInsights = ref([
  { id: 1, text: '85%的用户会从首页进入课程列表' },
  { id: 2, text: '课程详情页的转化率为70%，表现良好' },
  { id: 3, text: '学习完成率仅25%，需要优化学习体验' },
  { id: 4, text: '建议在课程详情页增加试听功能' }
])

// 用户生命周期数据
const lifecycleStages = ref([
  {
    name: '新用户',
    description: '注册7天内',
    userCount: 1234,
    avgDuration: '3天',
    conversionRate: 75,
    color: '#4f46e5',
    icon: 'User'
  },
  {
    name: '活跃用户',
    description: '持续使用中',
    userCount: 5678,
    avgDuration: '45天',
    conversionRate: 85,
    color: '#10b981',
    icon: 'TrendCharts'
  },
  {
    name: '沉睡用户',
    description: '30天未活跃',
    userCount: 890,
    avgDuration: '15天',
    conversionRate: 35,
    color: '#f59e0b',
    icon: 'Clock'
  },
  {
    name: '流失用户',
    description: '90天未登录',
    userCount: 234,
    avgDuration: '7天',
    conversionRate: 15,
    color: '#ef4444',
    icon: 'ArrowDown'
  }
])

// 学习活跃度数据
const activityData = ref([
  {
    label: '周一',
    hours: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.floor(Math.random() * 100)
    }))
  },
  {
    label: '周二',
    hours: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.floor(Math.random() * 100)
    }))
  },
  {
    label: '周三',
    hours: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.floor(Math.random() * 100)
    }))
  },
  {
    label: '周四',
    hours: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.floor(Math.random() * 100)
    }))
  },
  {
    label: '周五',
    hours: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.floor(Math.random() * 100)
    }))
  },
  {
    label: '周六',
    hours: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.floor(Math.random() * 100)
    }))
  },
  {
    label: '周日',
    hours: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      value: Math.floor(Math.random() * 100)
    }))
  }
])

// 活跃度等级
const activityLevels = ref([
  { label: '极高', color: '#ef4444', range: '80-100%' },
  { label: '高', color: '#f59e0b', range: '60-79%' },
  { label: '中等', color: '#10b981', range: '40-59%' },
  { label: '低', color: '#3b82f6', range: '20-39%' },
  { label: '极低', color: '#6b7280', range: '0-19%' }
])

// 导出历史记录
const exportHistory = ref([
  {
    name: '用户数据分析报告_2024-01-20.xlsx',
    time: new Date(Date.now() - 86400000),
    size: '2.5MB',
    format: 'excel'
  },
  {
    name: '学习行为分析_2024-01-19.pdf',
    time: new Date(Date.now() - 172800000),
    size: '1.8MB',
    format: 'pdf'
  },
  {
    name: '性能监控数据_2024-01-18.csv',
    time: new Date(Date.now() - 259200000),
    size: '856KB',
    format: 'csv'
  }
])

// 用户增长趋势数据
const userTrendData = ref({
  labels: [] as string[],
  datasets: [
    {
      label: '新增用户',
      data: [] as number[],
      borderColor: '#4f46e5',
      backgroundColor: 'rgba(79, 70, 229, 0.1)',
      tension: 0.4,
      fill: true
    },
    {
      label: '活跃用户',
      data: [] as number[],
      borderColor: '#10b981',
      backgroundColor: 'rgba(16, 185, 129, 0.1)',
      tension: 0.4,
      fill: true
    }
  ]
})

const userTrendOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    intersect: false,
    mode: 'index' as const,
  },
  plugins: {
    legend: {
      position: 'top' as const,
    },
    tooltip: {
      mode: 'index' as const,
      intersect: false,
      callbacks: {
        afterLabel: (context: any) => {
          return `点击查看详细数据`
        }
      }
    }
  },
  scales: {
    x: {
      display: true,
      title: {
        display: true,
        text: '日期'
      },
      grid: {
        display: false
      }
    },
    y: {
      display: true,
      title: {
        display: true,
        text: '用户数量'
      },
      beginAtZero: true
    }
  },
  onClick: (event: any, elements: any[]) => {
    if (elements.length > 0) {
      handleChartClick(elements[0])
    }
  }
})

// 实时更新定时器
let updateTimer: number | null = null

// 计算属性
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 方法实现
const toggleRealTime = () => {
  isRealTimeEnabled.value = !isRealTimeEnabled.value
  if (isRealTimeEnabled.value) {
    startRealTimeUpdates()
    ElMessage.success('实时更新已开启')
  } else {
    stopRealTimeUpdates()
    ElMessage.info('实时更新已关闭')
  }
}

const handleQuickTimeChange = (value: string | number | boolean | undefined) => {
  const stringValue = String(value)
  const now = new Date()
  let startDate: Date

  switch (stringValue) {
    case 'today':
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      filters.dateRange = [
        startDate.toISOString().split('T')[0],
        now.toISOString().split('T')[0]
      ]
      break
    case 'week':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      filters.dateRange = [
        startDate.toISOString().split('T')[0],
        now.toISOString().split('T')[0]
      ]
      break
    case 'month':
      startDate = new Date(now.getFullYear(), now.getMonth(), 1)
      filters.dateRange = [
        startDate.toISOString().split('T')[0],
        now.toISOString().split('T')[0]
      ]
      break
    case 'custom':
      filters.dateRange = []
      return
  }

  loadAnalyticsData()
}

const handleStatClick = (stat: any) => {
  // 统计卡片点击事件，可以跳转到详细页面或显示更多信息
  ElMessage.info(`查看${stat.label}详细数据`)
  // 这里可以实现数据钻取功能
}

const handleChartClick = (element: any) => {
  // 图表点击事件，实现数据钻取
  ElMessage.info('图表数据钻取功能')
  // 可以显示该时间点的详细数据
}

const toggleTrendComparison = () => {
  showComparison.value = !showComparison.value
  updateUserTrend()
}

const toggleCourseView = () => {
  showCourseDetails.value = !showCourseDetails.value
  // 可以在这里切换详细视图和简化视图的数据
  ElMessage.info(showCourseDetails.value ? '切换到详细视图' : '切换到简化视图')
}

const updateHeatmap = () => {
  // 根据热力图类型更新数据
  switch (heatmapType.value) {
    case 'activity':
      // 更新学习活跃度数据
      break
    case 'feature':
      // 更新功能使用数据
      break
    case 'path':
      // 更新访问路径数据
      break
  }
  ElMessage.info(`切换到${heatmapType.value === 'activity' ? '学习活跃度' : heatmapType.value === 'feature' ? '功能使用' : '访问路径'}视图`)
}

const handleHeatmapClick = (day: any, cell: any) => {
  ElMessage.info(`查看${day.label} ${cell.hour}:00的详细数据`)
  // 这里可以实现热力图的数据钻取功能
}

const getHeatmapColor = (value: number): string => {
  if (value >= 80) return '#10b981'
  if (value >= 60) return '#f59e0b'
  if (value >= 40) return '#06b6d4'
  if (value >= 20) return '#8b5cf6'
  return '#e5e7eb'
}

const updatePathAnalysis = () => {
  // 根据路径分析类型更新数据
  switch (pathAnalysisType.value) {
    case 'page':
      userPathData.value = [
        { name: '首页', count: 1000, color: '#4f46e5', flowWidth: 80, flowPercentage: 85 },
        { name: '课程列表', count: 850, color: '#10b981', flowWidth: 60, flowPercentage: 70 },
        { name: '课程详情', count: 595, color: '#f59e0b', flowWidth: 40, flowPercentage: 45 },
        { name: '开始学习', count: 268, color: '#ef4444', flowWidth: 20, flowPercentage: 25 },
        { name: '完成学习', count: 67, color: '#8b5cf6', flowWidth: 0, flowPercentage: 0 }
      ]
      pathInsights.value = [
        { id: 1, text: '85%的用户会从首页进入课程列表' },
        { id: 2, text: '课程详情页的转化率为70%，表现良好' },
        { id: 3, text: '学习完成率仅25%，需要优化学习体验' }
      ]
      break
    case 'feature':
      userPathData.value = [
        { name: '登录', count: 1000, color: '#4f46e5', flowWidth: 90, flowPercentage: 95 },
        { name: '浏览内容', count: 950, color: '#10b981', flowWidth: 70, flowPercentage: 80 },
        { name: '互动功能', count: 760, color: '#f59e0b', flowWidth: 50, flowPercentage: 60 },
        { name: '分享内容', count: 456, color: '#ef4444', flowWidth: 30, flowPercentage: 40 },
        { name: '付费转化', count: 182, color: '#8b5cf6', flowWidth: 0, flowPercentage: 0 }
      ]
      pathInsights.value = [
        { id: 1, text: '95%的用户登录后会浏览内容' },
        { id: 2, text: '互动功能使用率达80%，用户粘性强' },
        { id: 3, text: '付费转化率40%，有提升空间' }
      ]
      break
    case 'learning':
      userPathData.value = [
        { name: '选择课程', count: 1000, color: '#4f46e5', flowWidth: 85, flowPercentage: 90 },
        { name: '观看视频', count: 900, color: '#10b981', flowWidth: 65, flowPercentage: 75 },
        { name: '完成练习', count: 675, color: '#f59e0b', flowWidth: 45, flowPercentage: 55 },
        { name: '参与讨论', count: 371, color: '#ef4444', flowWidth: 25, flowPercentage: 30 },
        { name: '获得证书', count: 111, color: '#8b5cf6', flowWidth: 0, flowPercentage: 0 }
      ]
      pathInsights.value = [
        { id: 1, text: '90%的用户会观看课程视频' },
        { id: 2, text: '练习完成率75%，学习效果良好' },
        { id: 3, text: '讨论参与度30%，需要提升互动性' }
      ]
      break
  }
  ElMessage.info(`切换到${pathAnalysisType.value === 'page' ? '页面流向' : pathAnalysisType.value === 'feature' ? '功能使用' : '学习路径'}分析`)
}

// 学习活跃度相关方法
const getActivityColor = (value: number): string => {
  if (value >= 80) return '#ef4444'
  if (value >= 60) return '#f59e0b'
  if (value >= 40) return '#10b981'
  if (value >= 20) return '#3b82f6'
  return '#6b7280'
}

const updateActivityView = () => {
  // 根据视图类型更新活跃度数据
  switch (activityViewType.value) {
    case 'hourly':
      // 按小时分布 - 当前实现
      break
    case 'daily':
      // 按天分布 - 可以实现7天数据
      activityData.value = activityData.value.map(day => ({
        ...day,
        hours: Array.from({ length: 7 }, (_, i) => ({
          hour: i,
          value: Math.floor(Math.random() * 100)
        }))
      }))
      break
    case 'weekly':
      // 按周分布 - 可以实现4周数据
      activityData.value = activityData.value.map(day => ({
        ...day,
        hours: Array.from({ length: 4 }, (_, i) => ({
          hour: i,
          value: Math.floor(Math.random() * 100)
        }))
      }))
      break
  }
  ElMessage.info(`切换到${activityViewType.value === 'hourly' ? '按小时' : activityViewType.value === 'daily' ? '按天' : '按周'}分布视图`)
}

const toggleActivityAnimation = () => {
  activityAnimationEnabled.value = !activityAnimationEnabled.value
  ElMessage.info(activityAnimationEnabled.value ? '活跃度动画已开启' : '活跃度动画已关闭')
}

const handleActivityBarClick = (day: any, hour: any) => {
  ElMessage.info(`查看${day.label} ${hour.hour}:00的详细活跃度数据 (${hour.value}%)`)
}

// 数据导出相关方法
const handleExportData = async () => {
  exportLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 模拟数据导出
    const exportData = {
      format: exportFormat.value,
      dataTypes: exportDataTypes.value,
      dateRange: exportDateRange.value,
      timestamp: new Date().toISOString()
    }

    // 添加到导出历史
    const fileName = `数据分析报告_${new Date().toISOString().split('T')[0]}.${exportFormat.value}`
    exportHistory.value.unshift({
      name: fileName,
      time: new Date(),
      size: '1.2MB',
      format: exportFormat.value
    })

    // 限制历史记录数量
    if (exportHistory.value.length > 10) {
      exportHistory.value = exportHistory.value.slice(0, 10)
    }

    ElMessage.success(`数据导出成功：${fileName}`)
  } catch (error) {
    ElMessage.error('数据导出失败')
  } finally {
    exportLoading.value = false
  }
}

const handleScheduleExport = () => {
  ElMessage.info('定时导出功能开发中...')
}

const handleEmailReport = () => {
  ElMessage.info('邮件报告功能开发中...')
}

const downloadHistoryItem = (item: any) => {
  ElMessage.success(`下载文件：${item.name}`)
}

const deleteHistoryItem = (index: number) => {
  exportHistory.value.splice(index, 1)
  ElMessage.success('导出记录已删除')
}

const formatTime = (time: Date) => {
  return time.toLocaleString()
}

const handleCloseExportDialog = () => {
  showExportDialog.value = false
}

const refreshData = async () => {
  loading.value = true
  try {
    await loadAnalyticsData()
    // 添加数字动画效果
    enhancedStatistics.value.forEach((stat, index) => {
      setTimeout(() => {
        stat.isAnimating = true
        setTimeout(() => {
          stat.isAnimating = false
        }, 1000)
      }, index * 200)
    })
    ElMessage.success('数据刷新成功')
    lastUpdateTime.value = new Date().toLocaleString()
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const exportReport = async () => {
  exporting.value = true
  try {
    // 准备导出数据
    const reportData = {
      statistics: enhancedStatistics.value,
      userTrend: userTrendData.value,
      realTimeStats: realTimeStats.value,
      filters: filters
    }

    // 创建工作簿
    const wb = XLSX.utils.book_new()

    // 统计数据表
    const statsWs = XLSX.utils.json_to_sheet(
      enhancedStatistics.value.map(stat => ({
        '指标': stat.label,
        '数值': stat.value,
        '增长率': stat.growth + '%',
        '周期': stat.period,
        '备注': stat.subtitle
      }))
    )
    XLSX.utils.book_append_sheet(wb, statsWs, '核心指标')

    // 实时数据表
    const realTimeWs = XLSX.utils.json_to_sheet(
      realTimeStats.value.map(item => ({
        '指标': item.label,
        '数值': item.value,
        '变化': item.change + '%'
      }))
    )
    XLSX.utils.book_append_sheet(wb, realTimeWs, '实时数据')

    // 导出文件
    const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
    const blob = new Blob([wbout], { type: 'application/octet-stream' })
    saveAs(blob, `数据分析报告_${new Date().toISOString().split('T')[0]}.xlsx`)

    ElMessage.success('报告导出成功')
  } catch (error) {
    ElMessage.error('报告导出失败')
  } finally {
    exporting.value = false
  }
}

const handleDateChange = (dates: string[]) => {
  if (dates && dates.length === 2) {
    loadAnalyticsData()
  }
}

const handleFilterChange = () => {
  loadAnalyticsData()
}

const applyFilters = () => {
  loadAnalyticsData()
  ElMessage.success('筛选条件已应用')
}

const resetFilters = () => {
  filters.dateRange = []
  filters.userGroup = 'all'
  filters.deviceType = 'all'
  filters.region = 'all'
  filters.userTags = []
  filters.learningStage = 'all'
  quickTimeRange.value = 'month'
  loadAnalyticsData()
  ElMessage.success('筛选条件已重置')
}

const updateUserTrend = () => {
  generateUserTrendData()
}

const generateUserTrendData = () => {
  const days = userTrendPeriod.value === '7d' ? 7 : userTrendPeriod.value === '30d' ? 30 : 90
  const labels = []
  const newUsers = []
  const activeUsers = []

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    labels.push(date.toLocaleDateString())
    newUsers.push(Math.floor(Math.random() * 200) + 50)
    activeUsers.push(Math.floor(Math.random() * 500) + 200)
  }

  userTrendData.value.labels = labels
  userTrendData.value.datasets[0].data = newUsers
  userTrendData.value.datasets[1].data = activeUsers

  // 如果显示对比数据，添加对比数据集
  if (showComparison.value && userTrendData.value.datasets.length === 2) {
    userTrendData.value.datasets.push({
      label: '上期新增用户',
      data: newUsers.map(val => val * (0.8 + Math.random() * 0.4)),
      borderColor: '#94a3b8',
      backgroundColor: 'rgba(148, 163, 184, 0.1)',
      tension: 0.4,
      fill: false
    } as any)
  } else if (!showComparison.value && userTrendData.value.datasets.length > 2) {
    userTrendData.value.datasets = userTrendData.value.datasets.slice(0, 2)
  }
}

const loadAnalyticsData = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 生成模拟数据
    generateUserTrendData()

    // 更新统计数据
    enhancedStatistics.value[0].value = Math.floor(Math.random() * 5000) + 15000
    enhancedStatistics.value[1].value = Math.floor(Math.random() * 3000) + 8000
    enhancedStatistics.value[2].value = Math.floor(Math.random() * 20) + 70
    enhancedStatistics.value[3].value = Math.floor(Math.random() * 10) + 20

    // 更新实时数据
    realTimeStats.value.forEach(stat => {
      stat.value += Math.floor(Math.random() * 10) - 5
      stat.change = (Math.random() - 0.5) * 20
    })

    console.log('Analytics data loaded')
  } catch (error) {
    console.error('Failed to load analytics data:', error)
    throw error
  }
}

const startRealTimeUpdates = () => {
  if (updateTimer) return

  updateTimer = setInterval(() => {
    if (isRealTimeEnabled.value) {
      // 模拟实时数据更新
      enhancedStatistics.value[0].value += Math.floor(Math.random() * 5)
      enhancedStatistics.value[1].value += Math.floor(Math.random() * 3)

      // 更新实时统计
      realTimeStats.value.forEach(stat => {
        stat.value += Math.floor(Math.random() * 3) - 1
        stat.change = (Math.random() - 0.5) * 10
      })

      lastUpdateTime.value = new Date().toLocaleString()
    }
  }, 30000) // 每30秒更新一次
}

const stopRealTimeUpdates = () => {
  if (updateTimer) {
    clearInterval(updateTimer)
    updateTimer = null
  }
}

// 监听筛选器变化
watch(() => filters.userGroup, () => {
  if (filters.userGroup !== 'all') {
    handleFilterChange()
  }
})

watch(() => filters.deviceType, () => {
  if (filters.deviceType !== 'all') {
    handleFilterChange()
  }
})

watch(() => filters.region, () => {
  if (filters.region !== 'all') {
    handleFilterChange()
  }
})

watch(() => filters.userTags, () => {
  if (filters.userTags.length > 0) {
    handleFilterChange()
  }
})

watch(() => filters.learningStage, () => {
  if (filters.learningStage !== 'all') {
    handleFilterChange()
  }
})

// 生命周期
onMounted(() => {
  loadAnalyticsData()
  startRealTimeUpdates()
  lastUpdateTime.value = new Date().toLocaleString()

  // 设置默认时间范围
  handleQuickTimeChange('month')
})

onUnmounted(() => {
  stopRealTimeUpdates()
})
</script>

<style scoped>
@import '@/styles/design-system.scss';

.data-analysis-dashboard {
  padding: var(--spacing-6);
  background-color: var(--bg-secondary);
  min-height: 100vh;

  /* 页面头部 */
  .page-header {
    margin-bottom: var(--spacing-6);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: var(--spacing-4);

      .header-info {
        h1 {
          font-size: var(--font-size-3xl);
          font-weight: var(--font-weight-bold);
          color: var(--text-primary);
          margin: 0 0 var(--spacing-2) 0;
        }

        p {
          font-size: var(--font-size-base);
          color: var(--text-secondary);
          margin: 0;
        }
      }

      .header-actions {
        display: flex;
        gap: var(--spacing-3);
        flex-shrink: 0;

        .el-button {
          border-radius: var(--radius-lg);
          font-weight: var(--font-weight-medium);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
          }
        }
      }
    }
  }

  /* 筛选器卡片 */
  .filter-card {
    margin-bottom: var(--spacing-6);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);

    .filter-content {
      .el-form {
        .el-form-item {
          margin-bottom: var(--spacing-4);
          margin-right: var(--spacing-6);

          .el-form-item__label {
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
          }

          .el-date-editor,
          .el-select {
            border-radius: var(--radius-lg);
          }

          .el-radio-group {
            .el-radio-button {
              .el-radio-button__inner {
                border-radius: var(--radius-md);
                font-weight: var(--font-weight-medium);
                transition: all 0.3s ease;
              }
            }
          }
        }

        .el-button {
          border-radius: var(--radius-lg);
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }

  /* 统计概览 */
  .stats-overview {
    margin-bottom: var(--spacing-8);

    .stat-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      transition: all 0.3s ease;
      overflow: hidden;
      cursor: pointer;

      &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
      }

      &.primary {
        border-left: 4px solid var(--primary-color);

        .stat-icon {
          background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        }
      }

      &.success {
        border-left: 4px solid var(--success-color);

        .stat-icon {
          background: linear-gradient(135deg, var(--success-color), var(--success-light));
        }
      }

      &.warning {
        border-left: 4px solid var(--warning-color);

        .stat-icon {
          background: linear-gradient(135deg, var(--warning-color), var(--warning-light));
        }
      }

      &.info {
        border-left: 4px solid var(--info-color);

        .stat-icon {
          background: linear-gradient(135deg, var(--info-color), var(--info-light));
        }
      }

      .stat-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-6);

        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: var(--radius-xl);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-xl);
        }

        .stat-info {
          flex: 1;

          .stat-value {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
            transition: all 0.5s ease;

            &.animate-number {
              animation: numberPulse 1s ease-in-out;
            }
          }

          .stat-label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-2);
          }

          .stat-trend {
            display: flex;
            align-items: center;
            gap: var(--spacing-1);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);
            margin-bottom: var(--spacing-1);

            &.positive {
              color: var(--success-color);
            }

            &.negative {
              color: var(--error-color);
            }

            .trend-period {
              margin-left: var(--spacing-1);
              opacity: 0.7;
            }
          }

          .stat-subtitle {
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
          }
        }
      }
    }
  }

  /* 图表区域 */
  .charts-section {
    .chart-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      margin-bottom: var(--spacing-6);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: var(--shadow-md);
      }

      .el-card__header {
        border-bottom: 1px solid var(--border-light);
        padding: var(--spacing-5) var(--spacing-6);

        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          span {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
          }

          .chart-controls {
            display: flex;
            gap: var(--spacing-3);
            align-items: center;

            .el-select {
              width: 120px;
            }

            .el-button {
              font-size: var(--font-size-sm);
            }
          }
        }
      }

      .chart-container {
        padding: var(--spacing-4);
        min-height: 300px;
      }
    }

    /* 实时统计样式 */
    .real-time-stats {
      padding: var(--spacing-4);

      .real-time-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-3) 0;
        border-bottom: 1px solid var(--border-light);

        &:last-child {
          border-bottom: none;
        }

        .real-time-label {
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
          font-weight: var(--font-weight-medium);
        }

        .real-time-value {
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-bold);
          margin: 0 var(--spacing-2);
        }

        .real-time-change {
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-medium);
          padding: var(--spacing-1) var(--spacing-2);
          border-radius: var(--radius-md);

          &.positive {
            color: var(--success-color);
            background-color: var(--success-light);
          }

          &.negative {
            color: var(--error-color);
            background-color: var(--error-light);
          }
        }
      }
    }

    .activity-pulse {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-2);
      padding: var(--spacing-4);
      font-size: var(--font-size-sm);
      color: var(--text-secondary);

      .pulse-dot {
        width: 8px;
        height: 8px;
        background-color: var(--success-color);
        border-radius: 50%;
        animation: pulse 2s infinite;
      }
    }

    /* 用户行为热力图 */
    .behavior-heatmap {
      padding: var(--spacing-4);

      .heatmap-row {
        display: flex;
        align-items: center;
        margin-bottom: var(--spacing-2);

        .row-label {
          width: 50px;
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
          font-weight: var(--font-weight-medium);
        }

        .heatmap-cells {
          display: flex;
          gap: 2px;
          flex: 1;

          .heatmap-cell {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              transform: scale(1.2);
              box-shadow: var(--shadow-sm);
            }
          }
        }
      }
    }

    /* 设备统计 */
    .device-stats {
      padding: var(--spacing-4);
      margin-top: var(--spacing-4);

      .device-stat-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-3);
        padding: var(--spacing-2) 0;
        border-bottom: 1px solid var(--border-light);

        &:last-child {
          border-bottom: none;
        }

        .device-icon {
          width: 32px;
          height: 32px;
          border-radius: var(--radius-md);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-sm);
        }

        .device-info {
          flex: 1;

          .device-name {
            font-size: var(--font-size-sm);
            color: var(--text-primary);
            font-weight: var(--font-weight-medium);
          }

          .device-percentage {
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
          }
        }
      }
    }

    /* 用户路径分析 */
    .user-path-analysis {
      padding: var(--spacing-4);

      .path-flow {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--spacing-6);
        overflow-x: auto;
        padding: var(--spacing-4) 0;

        .path-step {
          display: flex;
          align-items: center;
          flex-shrink: 0;

          .step-node {
            padding: var(--spacing-4);
            border-radius: var(--radius-lg);
            color: white;
            text-align: center;
            min-width: 100px;
            box-shadow: var(--shadow-sm);

            .step-name {
              font-size: var(--font-size-sm);
              font-weight: var(--font-weight-medium);
              margin-bottom: var(--spacing-1);
            }

            .step-count {
              font-size: var(--font-size-lg);
              font-weight: var(--font-weight-bold);
            }
          }

          .path-arrow {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 var(--spacing-3);
            position: relative;

            .arrow-line {
              height: 3px;
              background: linear-gradient(to right, #4f46e5, #10b981);
              border-radius: 2px;
              margin-bottom: var(--spacing-1);
            }

            .arrow-head {
              width: 0;
              height: 0;
              border-left: 6px solid #10b981;
              border-top: 4px solid transparent;
              border-bottom: 4px solid transparent;
            }

            .flow-percentage {
              font-size: var(--font-size-xs);
              color: var(--text-secondary);
              font-weight: var(--font-weight-medium);
              margin-top: var(--spacing-1);
            }
          }
        }
      }

      .path-insights {
        background: var(--bg-light);
        padding: var(--spacing-4);
        border-radius: var(--radius-lg);
        border-left: 4px solid var(--primary-color);

        h4 {
          margin: 0 0 var(--spacing-3) 0;
          font-size: var(--font-size-base);
          color: var(--text-primary);
        }

        ul {
          margin: 0;
          padding: 0;
          list-style: none;

          li {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
            margin-bottom: var(--spacing-2);
            font-size: var(--font-size-sm);
            color: var(--text-secondary);

            &:last-child {
              margin-bottom: 0;
            }

            .el-icon {
              color: var(--primary-color);
            }
          }
        }
      }
    }

    /* 生命周期分析 */
    .lifecycle-analysis {
      padding: var(--spacing-4);

      .lifecycle-stage {
        margin-bottom: var(--spacing-5);
        padding: var(--spacing-4);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-lg);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: var(--shadow-sm);
          transform: translateY(-2px);
        }

        &:last-child {
          margin-bottom: 0;
        }

        .stage-header {
          display: flex;
          align-items: center;
          gap: var(--spacing-3);
          margin-bottom: var(--spacing-3);

          .stage-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-lg);
          }

          .stage-info {
            flex: 1;

            .stage-name {
              font-size: var(--font-size-base);
              font-weight: var(--font-weight-semibold);
              color: var(--text-primary);
              margin-bottom: var(--spacing-1);
            }

            .stage-description {
              font-size: var(--font-size-sm);
              color: var(--text-secondary);
            }
          }
        }

        .stage-metrics {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: var(--spacing-3);
          margin-bottom: var(--spacing-3);

          .metric-item {
            text-align: center;

            .metric-label {
              display: block;
              font-size: var(--font-size-xs);
              color: var(--text-secondary);
              margin-bottom: var(--spacing-1);
            }

            .metric-value {
              display: block;
              font-size: var(--font-size-sm);
              font-weight: var(--font-weight-semibold);
              color: var(--text-primary);

              &.positive {
                color: var(--success-color);
              }

              &.negative {
                color: var(--error-color);
              }
            }
          }
        }

        .stage-progress {
          .el-progress {
            .el-progress-bar__outer {
              background-color: var(--bg-light);
            }
          }
        }
      }
    }

    /* 学习活跃度热力图 */
    .activity-heatmap {
      padding: var(--spacing-4);

      .activity-day {
        display: flex;
        align-items: center;
        margin-bottom: var(--spacing-3);

        &:last-child {
          margin-bottom: 0;
        }

        .day-label {
          width: 60px;
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
          font-weight: var(--font-weight-medium);
          text-align: right;
          margin-right: var(--spacing-3);
        }

        .activity-bars {
          display: flex;
          gap: 2px;
          flex: 1;

          .activity-bar {
            width: 12px;
            min-height: 4px;
            border-radius: 2px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              transform: scaleY(1.1);
              opacity: 0.8;
            }

            &.animate-bar {
              animation: barGrow 0.6s ease-out forwards;
            }
          }
        }
      }

      .activity-legend {
        margin-top: var(--spacing-4);
        padding-top: var(--spacing-4);
        border-top: 1px solid var(--border-light);

        .legend-title {
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-semibold);
          color: var(--text-primary);
          margin-bottom: var(--spacing-3);
        }

        .legend-items {
          display: flex;
          flex-wrap: wrap;
          gap: var(--spacing-3);

          .legend-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);

            .legend-color {
              width: 12px;
              height: 12px;
              border-radius: 2px;
            }

            .legend-label {
              font-size: var(--font-size-xs);
              color: var(--text-primary);
              font-weight: var(--font-weight-medium);
            }

            .legend-range {
              font-size: var(--font-size-xs);
              color: var(--text-secondary);
            }
          }
        }
      }
    }

    /* 数据导出中心 */
    .export-center {
      padding: var(--spacing-4);

      .export-options {
        margin-bottom: var(--spacing-4);

        .option-group {
          margin-bottom: var(--spacing-4);

          &:last-child {
            margin-bottom: 0;
          }

          h4 {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0 0 var(--spacing-2) 0;
          }

          .el-radio-group,
          .el-checkbox-group {
            .el-radio,
            .el-checkbox {
              margin-bottom: var(--spacing-2);
              margin-right: var(--spacing-3);

              .el-radio__label,
              .el-checkbox__label {
                font-size: var(--font-size-xs);
              }
            }
          }

          .el-date-editor {
            width: 100%;
          }
        }
      }

      .export-actions {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-2);
        margin-bottom: var(--spacing-4);

        .el-button {
          justify-content: flex-start;
          border-radius: var(--radius-lg);
          font-weight: var(--font-weight-medium);
        }
      }

      .export-history {
        h4 {
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-semibold);
          color: var(--text-primary);
          margin: 0 0 var(--spacing-3) 0;
        }

        .history-list {
          max-height: 200px;
          overflow-y: auto;

          .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-2) 0;
            border-bottom: 1px solid var(--border-light);

            &:last-child {
              border-bottom: none;
            }

            .history-info {
              flex: 1;

              .history-name {
                font-size: var(--font-size-xs);
                color: var(--text-primary);
                margin-bottom: var(--spacing-1);
                word-break: break-all;
              }

              .history-time {
                font-size: var(--font-size-xs);
                color: var(--text-tertiary);
              }
            }

            .history-actions {
              display: flex;
              gap: var(--spacing-1);
              opacity: 0;
              transition: opacity 0.2s ease;

              .el-button {
                padding: 4px;
              }
            }

            &:hover .history-actions {
              opacity: 1;
            }
          }
        }
      }
    }
  }

  /* 实时更新指示器 */
  .real-time-indicator {
    position: fixed;
    bottom: var(--spacing-6);
    right: var(--spacing-6);
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    z-index: 1000;
    backdrop-filter: blur(10px);

    .pulse {
      animation: pulse 2s infinite;
    }

    .last-update {
      font-size: var(--font-size-xs);
      opacity: 0.8;
      margin-left: var(--spacing-2);
    }
  }
}

/* 动画 */
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes numberPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
    color: var(--primary-color);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes barGrow {
  0% {
    height: 0;
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* 响应式设计 */
@include respond-to('lg') {
  .data-analysis-dashboard {
    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
    }

    .filter-card .filter-content .el-form {
      .el-form-item {
        margin-right: 0;
        margin-bottom: var(--spacing-4);
      }
    }

    .charts-section {
      .el-col {
        margin-bottom: var(--spacing-6);
      }
    }
  }
}

@include respond-to('md') {
  .data-analysis-dashboard {
    padding: var(--spacing-4);

    .stats-overview {
      .el-col {
        margin-bottom: var(--spacing-4);
      }
    }

    .charts-section {
      .real-time-stats {
        .real-time-item {
          flex-direction: column;
          align-items: flex-start;
          gap: var(--spacing-2);

          .real-time-value {
            margin: 0;
          }
        }
      }
    }

    .real-time-indicator {
      bottom: var(--spacing-4);
      right: var(--spacing-4);
      font-size: var(--font-size-xs);
      padding: var(--spacing-2) var(--spacing-3);

      .last-update {
        display: none;
      }
    }
  }
}

@include respond-to('sm') {
  .data-analysis-dashboard {
    .filter-card .filter-content .el-form {
      .el-form-item {
        width: 100%;
        margin-right: 0;

        .el-radio-group {
          width: 100%;

          .el-radio-button {
            flex: 1;
          }
        }

        .el-select {
          width: 100%;
        }
      }
    }

    .stats-overview {
      .stat-card {
        .stat-content {
          flex-direction: column;
          text-align: center;
          gap: var(--spacing-3);

          .stat-icon {
            width: 50px;
            height: 50px;
          }
        }
      }
    }

    .charts-section {
      .user-path-analysis {
        .path-flow {
          flex-direction: column;
          align-items: stretch;

          .path-step {
            flex-direction: column;
            margin-bottom: var(--spacing-4);

            .step-node {
              min-width: auto;
              width: 100%;
            }

            .path-arrow {
              transform: rotate(90deg);
              margin: var(--spacing-2) 0;

              .arrow-line {
                width: 30px !important;
              }
            }
          }
        }
      }

      .lifecycle-analysis {
        .lifecycle-stage {
          .stage-metrics {
            grid-template-columns: 1fr;
            gap: var(--spacing-2);

            .metric-item {
              display: flex;
              justify-content: space-between;
              text-align: left;
            }
          }
        }
      }

      .behavior-heatmap {
        .heatmap-row {
          .heatmap-cells {
            .heatmap-cell {
              width: 8px;
              height: 8px;
            }
          }
        }
      }

      .device-stats {
        .device-stat-item {
          .device-icon {
            width: 28px;
            height: 28px;
          }
        }
      }

      .activity-heatmap {
        .activity-day {
          .activity-bars {
            .activity-bar {
              width: 8px;
            }
          }
        }

        .activity-legend {
          .legend-items {
            flex-direction: column;
            align-items: flex-start;
          }
        }
      }

      .export-center {
        .export-actions {
          .el-button {
            font-size: var(--font-size-xs);
            padding: var(--spacing-2) var(--spacing-3);
          }
        }

        .export-history {
          .history-list {
            .history-item {
              flex-direction: column;
              align-items: flex-start;
              gap: var(--spacing-2);

              .history-actions {
                opacity: 1;
                align-self: flex-end;
              }
            }
          }
        }
      }
    }
  }
}

@include respond-to('xs') {
  .data-analysis-dashboard {
    padding: var(--spacing-3);

    .page-header {
      .header-content {
        .header-actions {
          flex-direction: column;
          width: 100%;

          .el-button {
            width: 100%;
            margin-bottom: var(--spacing-2);

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }

    .charts-section {
      .chart-card {
        .chart-container {
          padding: var(--spacing-2);
        }

        .el-card__header {
          padding: var(--spacing-3);

          .chart-header {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--spacing-2);

            .chart-controls {
              width: 100%;
              justify-content: space-between;

              .el-select {
                width: auto;
                min-width: 100px;
              }
            }
          }
        }
      }

      .real-time-stats {
        .real-time-item {
          padding: var(--spacing-2) 0;

          .real-time-value {
            font-size: var(--font-size-base);
          }
        }
      }
    }
  }
}

// 导出弹窗样式
.export-dialog-content {
  .option-group {
    margin-bottom: var(--spacing-4);

    h4 {
      margin: 0 0 var(--spacing-2) 0;
      font-size: var(--font-size-sm);
      font-weight: 600;
      color: var(--text-color-primary);
    }

    .el-radio-group {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-3);
    }

    .el-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-3);
    }

    .el-date-picker {
      width: 100%;
    }
  }

  .export-history {
    h4 {
      margin: 0 0 var(--spacing-2) 0;
      font-size: var(--font-size-sm);
      font-weight: 600;
      color: var(--text-color-primary);
    }

    .history-list {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid var(--border-color-light);
      border-radius: var(--border-radius-base);
      padding: var(--spacing-2);

      .history-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-2);
        border-bottom: 1px solid var(--border-color-lighter);
        transition: background-color 0.3s ease;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: var(--fill-color-lighter);
        }

        .history-info {
          flex: 1;

          .history-name {
            font-size: var(--font-size-sm);
            font-weight: 500;
            color: var(--text-color-primary);
            margin-bottom: var(--spacing-1);
          }

          .history-time {
            font-size: var(--font-size-xs);
            color: var(--text-color-secondary);
          }
        }

        .history-actions {
          display: flex;
          gap: var(--spacing-1);
          opacity: 0.7;
          transition: opacity 0.3s ease;

          &:hover {
            opacity: 1;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-2);
}
</style>
