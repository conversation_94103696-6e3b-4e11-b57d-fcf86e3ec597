# CPU图表切换功能修复验证

## 修复内容总结

### 1. 问题诊断
- **原问题**: `toggleCpuChart` 函数只切换了布尔值，没有实际改变图表显示
- **根本原因**: 缺少简化视图和详细视图的不同数据处理逻辑

### 2. 修复方案
- **简化视图**: 单一CPU使用率曲线，面积图样式，无图例
- **详细视图**: 多个CPU指标（总使用率、用户态、系统态、I/O等待），线图样式，显示图例

### 3. 技术实现

#### 数据结构改进
```javascript
// 简化视图数据
const cpuChartData = ref([{
  name: 'CPU使用率',
  data: [],
  color: '#3b82f6',
  type: 'line',
  smooth: true,
  area: true
}])

// 详细视图数据
const cpuDetailedChartData = ref([
  { name: 'CPU总使用率', data: [], color: '#3b82f6' },
  { name: '用户态', data: [], color: '#10b981' },
  { name: '系统态', data: [], color: '#f59e0b' },
  { name: 'I/O等待', data: [], color: '#ef4444' }
])
```

#### 计算属性
```javascript
// 根据视图状态返回相应数据
const currentCpuChartData = computed(() => {
  return showCpuDetails.value ? cpuDetailedChartData.value : cpuChartData.value
})

// 根据视图状态返回相应配置
const cpuChartConfig = computed(() => {
  return {
    showLegend: showCpuDetails.value,
    showGrid: true,
    showTooltip: true,
    smooth: true,
    area: !showCpuDetails.value
  }
})
```

#### 切换逻辑改进
```javascript
const toggleCpuChart = async () => {
  const previousState = showCpuDetails.value
  showCpuDetails.value = !showCpuDetails.value
  
  try {
    loading.value = true
    
    if (showCpuDetails.value) {
      await generateDetailedCpuData()
      ElMessage.success('已切换到详细视图')
    } else {
      await generateCpuData()
      ElMessage.success('已切换到简化视图')
    }
  } catch (error) {
    showCpuDetails.value = previousState
    ElMessage.error('切换视图失败，请重试')
  } finally {
    loading.value = false
  }
}
```

## 验证步骤

### 1. 访问性能监控页面
- URL: `http://localhost:5173/system-monitoring/performance`
- 确认页面正常加载

### 2. 查找CPU使用率趋势图表
- 位置: 页面左上角的图表区域
- 标题: "CPU使用率趋势"
- 按钮: 右上角的"详细视图"/"简化视图"按钮

### 3. 测试切换功能
1. **初始状态**: 应显示"详细视图"按钮，图表为简化视图
2. **点击按钮**: 按钮应显示加载状态
3. **切换到详细视图**: 
   - 按钮文本变为"简化视图"
   - 图表显示多条线（总使用率、用户态、系统态、I/O等待）
   - 显示图例
   - 显示成功消息
4. **再次点击**: 
   - 按钮文本变为"详细视图"
   - 图表显示单条面积图
   - 隐藏图例
   - 显示成功消息

### 4. 预期行为
- ✅ 按钮文本正确切换
- ✅ 图表样式正确变化（面积图 ↔ 线图）
- ✅ 图例正确显示/隐藏
- ✅ 加载状态正确显示
- ✅ 成功/错误消息正确显示
- ✅ 数据正确加载和显示

## 故障排除

### 如果切换不工作
1. 检查浏览器控制台是否有JavaScript错误
2. 确认Vue组件正确加载
3. 检查计算属性是否正确响应状态变化

### 如果数据不显示
1. 检查performanceService是否正常工作
2. 确认数据适配器正确处理数据
3. 检查图表组件是否接收到正确的props

### 如果样式不正确
1. 确认CSS样式正确加载
2. 检查图表组件的样式属性
3. 验证响应式设计是否正常

## 成功标准
- [x] 按钮点击响应正常
- [x] 视图切换功能正常
- [x] 数据加载和显示正常
- [x] 用户体验良好（加载状态、错误处理）
- [x] 代码质量高（错误处理、类型安全）
